{% extends "main.html" %}
{% load static %}

{% block title %}REPConnect - Certificate Management{% endblock title %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'css/certificate/certificate.css' %}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intro.js/minified/introjs.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intro.js/themes/introjs-dark.min.css" id="introjs-dark-theme" disabled>
{% endblock %}

{% block content %}
<div class="page-content" id="page-content">
    <header class="page-header">
        <div class="page-header-content">
            <h2>Certificate Management</h2>
            <p>Upload and organize certificates for employees efficiently</p>
        </div>
    </header>
    <div class="dashboard-stats" data-intro="View key statistics about certificates and employees" data-step="2">
        <div class="modern-stats-grid">
            <div class="modern-stat-card" data-intro="Total number of employees in the system" data-step="3">
                <div class="modern-stat-header">
                    <div class="modern-stat-icon blue"><i class="fas fa-users"></i></div>
                </div>
                <div class="modern-stat-label">Total Employees</div>
                <div class="modern-stat-value" id="statTotalEmployees">{{ stat_total_employees }}</div>
                <div class="modern-stat-change {% if employees_positive %}positive{% else %}negative{% endif %}">
                    <span class="modern-stat-change-icon">
                        <i class="fas {% if employees_positive %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                    </span>
                    {{ employees_percent }}%
                    <span class="modern-stat-change-text">vs prev. month</span>
                </div>
            </div>
            <div class="modern-stat-card" data-intro="Total number of certificates uploaded" data-step="4">
                <div class="modern-stat-header">
                    <div class="modern-stat-icon orange"><i class="fas fa-file-alt"></i></div>
                </div>
                <div class="modern-stat-label">Total Certificates</div>
                <div class="modern-stat-value" id="statTotalCertificates">{{ stat_total_certificates }}</div>
                <div class="modern-stat-change {% if certificates_positive %}positive{% else %}negative{% endif %}">
                    <span class="modern-stat-change-icon">
                        <i class="fas {% if certificates_positive %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                    </span>
                    {{ certificates_percent }}%
                    <span class="modern-stat-change-text">vs prev. month</span>
                </div>
            </div>
            <div class="modern-stat-card" data-intro="Certificates uploaded this month" data-step="5">
                <div class="modern-stat-header">
                    <div class="modern-stat-icon green"><i class="fas fa-calendar-alt"></i></div>
                </div>
                <div class="modern-stat-label">Certificates This Month</div>
                <div class="modern-stat-value" id="statCertificatesThisMonth">{{ stat_certificates_this_month }}</div>
                <div class="modern-stat-change {% if certs_month_positive %}positive{% else %}negative{% endif %}">
                    <span class="modern-stat-change-icon">
                        <i class="fas {% if certs_month_positive %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                    </span>
                    {{ certs_month_percent }}%
                    <span class="modern-stat-change-text">vs prev. month</span>
                </div>
            </div>
            <div class="modern-stat-card" data-intro="Certificates that haven't been viewed yet" data-step="6">
                <div class="modern-stat-header">
                    <div class="modern-stat-icon red"><i class="fas fa-eye-slash"></i></div>
                </div>
                <div class="modern-stat-label">Unseen Certificates</div>
                <div class="modern-stat-value" id="statUnseenCertificates">{{ stat_unseen_certificates }}</div>
                <div class="modern-stat-change {% if stat_unseen_certificates < unseen_prev %}positive{% else %}negative{% endif %}">
                    <span class="modern-stat-change-icon">
                        <i class="fas {% if stat_unseen_certificates < unseen_prev %}fa-arrow-down{% else %}fa-arrow-up{% endif %}"></i>
                    </span>
                    {{ unseen_percent }}%
                    <span class="modern-stat-change-text">vs prev. month</span>
                </div>
            </div>
        </div>
    </div>

    <div class="component-card">
        <div class="table-actions table-actions-bar" data-intro="Use these tools to search, filter, and manage certificates" data-step="7">
            <div class="table-actions-left" data-intro="Search for employees by name, ID number, or email" data-step="8">
                <form class="search-box">
                    <input type="text" class="search-input" id="searchInput" placeholder="Search..." name="search" value="{{ search }}" />
                    <span class="search-icon"><i class="fas fa-search"></i></span>
                    {% if search %}
                    <span class="search-clear" onclick="clearSearch()">
                        <i class="fas fa-times"></i>
                    </span>
                    {% endif %}
                </form>
            </div>
            <div class="table-actions-right" style="position:relative;" data-intro="Import certificates in bulk or take a tour of this page" data-step="9">
                <button class="btn btn-action" onclick="openModal('bulkUploadModal')" data-intro="Upload multiple certificates at once using employee ID numbers as filenames" data-step="10">
                    <i class="fas fa-file-import"></i>
                    Import
                </button>
                <button class="tour-btn" onclick="startProductTour()" data-intro="Take a guided tour of the Certificate Management page" data-step="1">
                    <span class="tour-icon"><i class="fa-regular fa-circle-question"></i></span>
                    <span class="tour-tooltip">Page Tour</span>
                </button>
            </div>
        </div>
        
        {% if employees_with_certificates %}
            <table class="data-table employee-table" data-intro="View all employees with certificates. Click on any row to expand and see their certificates" data-step="11">
                <thead>
                    <tr>
                        <th class="col-avatar"></th>
                        <th>ID Number</th>
                        <th>Employee Name</th>
                        <th class="col-email">Email Address</th>
                        <th>Certificates</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                {% for employee in employees_with_certificates %}
                    <tr class="employee-row" onclick="toggleEmployeeCertificates({{ employee.id }})" style="cursor:pointer;">
                        <td class="col-avatar">
                            {% if employee.avatar %}
                                <img src="{{ employee.avatar.url }}" alt="{{ employee.firstname }}" class="employee-avatar">
                            {% else %}
                                <div class="avatar-placeholder"><i class="fas fa-user"></i></div>
                            {% endif %}
                        </td>
                        <td>{{ employee.idnumber }}</td>
                        <td>{{ employee.firstname }} {{ employee.lastname }}</td>
                        <td class="col-email">{{ employee.email }}</td>
                        <td>{{ employee.certificate_count }} certificate{{ employee.certificate_count|pluralize }}</td>
                        <td class="expand-icon"><i class="fas fa-chevron-down"></i></td>
                    </tr>
                    <tr class="certificates-row" id="certificates-{{ employee.id }}" style="display:none;">
                        <td colspan="6" class="certificates-cell">
                            <div class="certificates-content">
                                <div class="certificate-list card-list-flex">
                                    {% for certificate in employee.certificates.all %}
                                    <div class="certificate-card-list-item-centered" data-certificate-id="{{ certificate.id }}">
                                        <div class="certificate-card-filetype">
                                            {% if certificate.is_pdf %}
                                                <span class="filetype-icon pdf"><i class="fas fa-file-pdf"></i></span>
                                            {% elif certificate.is_image %}
                                                <span class="filetype-icon image"><i class="fas fa-file-image"></i></span>
                                            {% else %}
                                                <span class="filetype-icon other"><i class="fas fa-file"></i></span>
                                            {% endif %}
                                        </div>
                                        <div class="certificate-card-info">
                                            <div class="certificate-card-title">{{ certificate.title }}</div>
                                            <div class="certificate-card-date">{{ certificate.created_at|date:"M d, Y" }}</div>
                                        </div>
                                        <div class="certificate-card-actions-dropdown">
                                            <button class="btn btn-icon btn-more" title="More actions" onclick="toggleCertDropdown(this)">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <div class="certificate-dropdown-menu">
                                                <button class="dropdown-item view-certificate" data-certificate-id="{{ certificate.id }}"><i class="fas fa-eye"></i> View</button>
                                                <button class="dropdown-item delete-certificate" data-certificate-id="{{ certificate.id }}"><i class="fas fa-trash"></i> Delete</button>
                                            </div>
                                        </div>
                                    </div>
                                    {% empty %}
                                    <div class="certificate-list-empty">No certificates found for this employee.</div>
                                    {% endfor %}
                                </div>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>

            <div class="pagination" id="paginationContainer" data-intro="Navigate through pages of results" data-step="12">
                <div class="pagination-info">
                    Showing <span id="startRecord">{{ page_obj.start_index }}</span> to <span id="endRecord">{{ page_obj.end_index }}</span> of <span id="totalRecords">{{ page_obj.paginator.count }}</span> entries
                </div>
                <div class="pagination-controls" id="paginationControls">
                    {% if page_obj.has_previous %}
                        <a class="pagination-btn" id="prevPage" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% else %}
                        <span class="pagination-btn" id="prevPage" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </span>
                    {% endif %}
                    <div id="pageNumbers">
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="pagination-btn active">{{ num }}</span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a class="pagination-btn" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}">{{ num }}</a>
                            {% endif %}
                        {% endfor %}
                    </div>
                    {% if page_obj.has_next %}
                        <a class="pagination-btn" id="nextPage" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% else %}
                        <span class="pagination-btn" id="nextPage" disabled>
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    {% endif %}
                </div>
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-state-icon">
                    {% if search %}
                        <i class="fas fa-search"></i>
                    {% else %}
                    <i class="fas fa-users"></i>
                    {% endif %}
                </div>
                <h3>
                    {% if search %}
                        No Results Found
                    {% else %}
                        No Certificates Found
                    {% endif %}
                </h3>
                <p>
                    {% if search %}
                        No employees found matching "{{ search }}". Try adjusting your search terms.
                    {% else %}
                        No employees have certificates yet. Upload some certificates to get started!
                    {% endif %}
                </p>
            </div>
        {% endif %}
    </div>
</div>

<div id="certificateViewModal" class="modal modal-xl">
    <div class="modal-overlay"></div>
    <div class="modal-content modal-lg">
        <div class="modal-header">
            <h3 id="certificateViewTitle">Certificate</h3>
            <button class="modal-close" onclick="closeCertificateViewModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div id="certificateViewContent" class="certificate-viewer">
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closeCertificateViewModal()">
                Close
            </button>
        </div>
    </div>
</div>

<div id="replaceCertificateModal" class="modal">
    <div class="modal-overlay"></div>
    <div class="modal-content modal-md">
        <div class="modal-header">
            <h3>Replace Certificate</h3>
            <button class="modal-close" onclick="closeReplaceCertificateModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="replaceCertificateForm" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="form-group">
                    <label class="form-label">New Certificate File</label>
                    <div class="file-upload">
                        <input type="file" id="replaceCertificateFile" name="certificate_file" class="file-input" accept=".pdf,.jpg,.jpeg,.png" required>
                        <label for="replaceCertificateFile" class="file-label">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Choose new file</span>
                            <small>PDF, JPG, JPEG, PNG • Max 10MB</small>
                        </label>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" onclick="submitReplaceCertificate()">
                <i class="fas fa-save"></i>
                Replace Certificate
            </button>
            <button class="btn btn-outline" onclick="closeReplaceCertificateModal()">
                Cancel
            </button>
        </div>
    </div>
</div>

<div id="bulkUploadModal" class="modal modal-md">
    <div class="modal-overlay"></div>
    <div class="modal-content modal-md">
        <div class="modal-header">
            <h3>Bulk Upload Certificates</h3>
            <button class="modal-close" onclick="closeModal('bulkUploadModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="bulkUploadForm" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="form-group">
                    <label class="form-label">Certificate Title <span class="required">*</span></label>
                    <div class="input-group">
                        <i class="fas fa-award input-icon"></i>
                        {{ form.title }}
                    </div>
                    <small class="form-help">Required: Enter the certificate title.</small>
                </div>
                <div class="form-group">
                    <label class="form-label">Certificate Files</label>
                    <div class="file-upload">
                        {{ form.certificates }}
                        <label for="{{ form.certificates.id_for_label }}" class="file-label">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Choose files or drag here</span>
                            <small>JPG, JPEG, PNG • Max 10MB each</small>
                            <small>Filename should be employee ID number</small>
                        </label>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">  
            <button type="button" class="btn btn-outline" onclick="closeModal('bulkUploadModal')">Cancel</button>
            <button type="submit" class="btn btn-primary" id="uploadBtn" form="bulkUploadForm">
                <i class="fas fa-upload"></i>
                Upload Certificates
            </button>
        </div>
    </div>
</div>

<div id="deleteCertificateModal" class="modal modal-md">
    <div class="modal-overlay"></div>
    <div class="modal-content modal-sm">
        <div class="modal-header">
            <h3>Delete Certificate</h3>
            <button class="modal-close" onclick="closeDeleteCertificateModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <p>Are you sure you want to delete this certificate? This action cannot be undone.</p>
        </div>
        <div class="modal-footer">
            <button class="btn btn-error" id="confirmDeleteCertificateBtn">Delete</button>
            <button class="btn btn-outline" onclick="closeDeleteCertificateModal()">Cancel</button>
        </div>
    </div>
</div>

<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-spinner">
        <div class="spinner"></div>
        <p>Processing...</p>
    </div>
</div>
{% endblock content %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/intro.js/minified/intro.min.js"></script>
<script>
function toggleCertDropdown(btn) {
    const menu = btn.nextElementSibling;
    const isOpen = menu.classList.contains('open');
    document.querySelectorAll('.certificate-dropdown-menu.open').forEach(m => m.classList.remove('open'));
    if (!isOpen) {
        menu.classList.add('open');
        document.addEventListener('click', closeDropdown, { once: true });
    }
    function closeDropdown(e) {
        if (!menu.contains(e.target) && e.target !== btn) {
            menu.classList.remove('open');
        }
    }
}

function clearSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = '';
        if (window.adminCertificateManager) {
            window.adminCertificateManager.performSearch('');
        }
        searchInput.focus();
    }
}

function addStepNumberToIntroJs(tour) {
    tour.onafterchange(function(targetElement) {
        const tooltip = document.querySelector('.introjs-tooltip');
        if (!tooltip) return;
        let stepNumberDiv = tooltip.querySelector('.introjs-step-number');
        if (stepNumberDiv) stepNumberDiv.remove();
        const currentStep = tour._currentStep + 1;
        const totalSteps = tour._options.steps.length;
        stepNumberDiv = document.createElement('div');
        stepNumberDiv.className = 'introjs-step-number';
        stepNumberDiv.textContent = `Step ${currentStep} of ${totalSteps}`;
        stepNumberDiv.style.fontWeight = '500';
        stepNumberDiv.style.fontSize = '0.95em';
        stepNumberDiv.style.marginBottom = '8px';
        stepNumberDiv.style.textAlign = 'center';
        const progressBar = tooltip.querySelector('.introjs-progress');
        if (progressBar) {
            tooltip.insertBefore(stepNumberDiv, progressBar);
        }
    });
}

function startProductTour() {
    const tour = introJs();
    tour.setOptions({
        steps: [
            {
                element: '.tour-btn',
                intro: 'Welcome to the Certificate Management page! This tour will guide you through all the features.',
                position: 'left'
            },
            {
                element: '.dashboard-stats',
                intro: 'Here you can view key statistics about certificates and employees in your organization.',
                position: 'bottom'
            },
            {
                element: '.modern-stat-card:nth-child(1)',
                intro: 'Total number of employees in the system',
                position: 'bottom'
            },
            {
                element: '.modern-stat-card:nth-child(2)',
                intro: 'Total number of certificates uploaded',
                position: 'bottom'
            },
            {
                element: '.modern-stat-card:nth-child(3)',
                intro: 'Certificates uploaded this month',
                position: 'bottom'
            },
            {
                element: '.modern-stat-card:nth-child(4)',
                intro: 'Certificates that haven\'t been viewed yet',
                position: 'bottom'
            },
            {
                element: '.table-actions-bar',
                intro: 'Use these tools to search, filter, and manage certificates efficiently.',
                position: 'bottom'
            },
            {
                element: '.table-actions-left',
                intro: 'Search for employees by name, ID number, or email address. The search works in real-time!',
                position: 'bottom'
            },
            {
                element: '.table-actions-right',
                intro: 'Import certificates in bulk or take a tour of this page anytime.',
                position: 'left'
            },
            {
                element: '.btn-action[onclick*="bulkUploadModal"]',
                intro: 'Upload multiple certificates at once using employee ID numbers as filenames.',
                position: 'left'
            },
            {
                element: '.data-table',
                intro: 'View all employees with certificates. Click on any row to expand and see their certificates.',
                position: 'top'
            },
            {
                element: '#paginationContainer',
                intro: 'Navigate through pages of results. The pagination updates automatically with search results.',
                position: 'top'
            }
        ],
        showProgress: true,
        showBullets: false,
        nextLabel: 'Next',
        prevLabel: 'Back',
        doneLabel: 'Finish',
        skipLabel: 'Skip Tour',
        exitOnOverlayClick: false,
        exitOnEsc: true
    });
    addStepNumberToIntroJs(tour);
    tour.onbeforechange(function(targetElement) {
        document.body.classList.add('introjs-blur-active');
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
        if (isDark) {
            document.getElementById('introjs-dark-theme').disabled = false;
        } else {
            document.getElementById('introjs-dark-theme').disabled = true;
        }
    });
    tour.onexit(function() {
        document.body.classList.remove('introjs-blur-active');
        console.log('Tour exited');
    });
    tour.oncomplete(function() {
        document.body.classList.remove('introjs-blur-active');
        console.log('Tour completed');
    });
    tour.start();
}

let certificateIdToDelete = null;

document.addEventListener('click', function(e) {
    if (e.target.classList.contains('delete-certificate')) {
        certificateIdToDelete = e.target.getAttribute('data-certificate-id');
        document.getElementById('deleteCertificateModal').classList.add('show');
    }
});

document.getElementById('confirmDeleteCertificateBtn').onclick = function() {
    if (certificateIdToDelete) {
        if (window.adminCertificateManager) {
            window.adminCertificateManager.deleteCertificate(certificateIdToDelete);
        }
        closeDeleteCertificateModal();
        certificateIdToDelete = null;
    }
};

function closeDeleteCertificateModal() {
    document.getElementById('deleteCertificateModal').classList.remove('show');
    certificateIdToDelete = null;
}
</script>
<script src="{% static 'js/certificate/admin-cert.js' %}"></script>
{% endblock %}