from django.contrib import admin
from .models import Payslip, Loan, Allowance, OJTPayslipData, LoanType, AllowanceType

@admin.register(Payslip)
class PayslipAdmin(admin.ModelAdmin):
    list_display = ['employee', 'cutoff_from', 'cutoff_to', 'date_uploaded', 'uploaded_by']
    list_filter = ['date_uploaded', 'cutoff_from']
    search_fields = ['employee__name', 'employee__idnumber', 'employee__firstname', 'employee__lastname']
    date_hierarchy = 'cutoff_from'
    list_per_page = 25

    fieldsets = (
        ('Employee Information', {
            'fields': ('employee',)
        }),
        ('Payroll Period', {
            'fields': ('cutoff_from', 'cutoff_to')
        }),
        ('File', {
            'fields': ('file_path',)
        }),
        ('Metadata', {
            'fields': ('uploaded_by', 'date_uploaded'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['date_uploaded']


@admin.register(Loan)
class LoanAdmin(admin.ModelAdmin):
    list_display = ['employee', 'loan_type', 'balance', 'monthly_deduction', 'created_at']
    list_filter = ['loan_type', 'created_at']
    search_fields = ['employee__name', 'employee__idnumber', 'employee__firstname', 'employee__lastname']
    date_hierarchy = 'created_at'
    list_per_page = 25

    fieldsets = (
        ('Employee Information', {
            'fields': ('employee',)
        }),
        ('Loan Details', {
            'fields': ('loan_type', 'amount', 'balance', 'monthly_deduction')
        }),
    )

    readonly_fields = ['created_at']


@admin.register(Allowance)
class AllowanceAdmin(admin.ModelAdmin):
    list_display = ['employee', 'allowance_type', 'amount', 'deposit_date', 'created_at']
    list_filter = ['allowance_type', 'deposit_date']
    search_fields = ['employee__name', 'employee__idnumber', 'employee__firstname', 'employee__lastname']
    date_hierarchy = 'deposit_date'
    list_per_page = 25

    fieldsets = (
        ('Employee Information', {
            'fields': ('employee',)
        }),
        ('Allowance Details', {
            'fields': ('allowance_type', 'amount')
        }),
        ('Deposit Info', {
            'fields': ('deposit_date',),
        }),
        ('Metadata', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['created_at']


@admin.register(OJTPayslipData)
class OJTPayslipDataAdmin(admin.ModelAdmin):
    list_display = ['employee', 'cut_off', 'regular_day', 'allowance_day', 'grand_total', 'net_ojt_share', 'created_at']
    list_filter = ['created_at', 'cut_off']
    search_fields = ['employee__name', 'employee__idnumber', 'employee__firstname', 'employee__lastname']
    date_hierarchy = 'created_at'
    list_per_page = 25

    fieldsets = (
        ('Employee Information', {
            'fields': ('employee', 'cut_off')
        }),
        ('Basic Pay', {
            'fields': ('regular_day', 'allowance_day', 'total_allowance', 'nd_allowance', 'grand_total')
        }),
        ('School & OJT Share', {
            'fields': ('basic_school_share', 'basic_ojt_share', 'net_ojt_share')
        }),
        ('Allowances', {
            'fields': ('rice_allowance', 'ot_allowance', 'nd_ot_allowance', 'ot_pay_allowance', 'total_allow'),
            'classes': ('collapse',)
        }),
        ('Holiday & Overtime', {
            'fields': ('special_holiday', 'legal_holiday', 'satoff_allowance', 'rd_ot', 'holiday_hours', 'rd_ot_days'),
            'classes': ('collapse',)
        }),
        ('Deductions & Adjustments', {
            'fields': ('deduction', 'deduction_2', 'adjustment'),
            'classes': ('collapse',)
        }),
        ('Other', {
            'fields': ('line_number', 'perfect_attendance'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['created_at']

admin.site.register(LoanType)
admin.site.register(AllowanceType)
