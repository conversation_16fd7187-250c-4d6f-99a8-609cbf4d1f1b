from django import forms
from django.core.exceptions import ValidationError
from userlogin.models import EmployeeLogin
from .models import Payslip, Loan, Allowance
import os

class PayslipUploadForm(forms.Form):
    EMPLOYEE_TYPE_CHOICES = [
        ('regular', 'Regular/Probationary'),
        ('ojt', 'OJT'),
    ]
    
    employee_type = forms.ChoiceField(
        choices=EMPLOYEE_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-input',
            'id': 'employee-type-select'
        })
    )
    cutoff_from = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input',
            'required': True
        })
    )
    cutoff_to = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input',
            'required': True
        })
    )
    file = forms.FileField(
        widget=forms.ClearableFileInput(attrs={
            'class': 'form-input',
            'accept': '.pdf,.jpg,.jpeg,.png,.xlsx',
            'id': 'payslip-file'
        }),
        required=True
    )
    
    def clean(self):
        cleaned_data = super().clean()
        cutoff_from = cleaned_data.get('cutoff_from')
        cutoff_to = cleaned_data.get('cutoff_to')
        
        if cutoff_from and cutoff_to:
            if cutoff_from >= cutoff_to:
                raise ValidationError("Cutoff 'From' date must be before 'To' date.")
        
        return cleaned_data

    class Meta:
        model = Payslip
        fields = ['employee', 'cutoff_from', 'cutoff_to', 'file_path', 'amount']
        widgets = {
            'file_path': forms.ClearableFileInput()
        }

class EmployeeSearchForm(forms.Form):
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Search by name, ID number, or department...',
            'id': 'employee-search'
        })
    )
    department = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Filter by department...',
            'id': 'department-filter'
        })
    )
    employment_type = forms.ChoiceField(
        choices=[
            ('', 'All Types'),
            ('regular', 'Regular'),
            ('probationary', 'Probationary'),
            ('ojt', 'OJT')
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-input',
            'id': 'employment-type-filter'
        })
    )


class EmailSelectionForm(forms.Form):
    EMAIL_CHOICES = [
        ('personal', 'Personal Email'),
        ('work', 'Work Email'),
    ]
    
    email_type = forms.ChoiceField(
        choices=EMAIL_CHOICES,
        widget=forms.RadioSelect(attrs={
            'class': 'form-radio'
        }),
        required=True
    )
    
    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if user:
            personal_email = getattr(user, 'email', '')
            work_email = getattr(user, 'work_email', '') if hasattr(user, 'work_email') else ''
            
            choices = []
            if personal_email:
                choices.append(('personal', f'Personal Email ({personal_email})'))
            if work_email:
                choices.append(('work', f'Work Email ({work_email})'))
            
            if not choices:
                choices = [('personal', 'Personal Email (No email found)')]
            
            self.fields['email_type'].choices = choices


class LoanForm(forms.ModelForm):
    class Meta:
        model = Loan
        fields = ['employee', 'loan_type', 'amount', 'balance', 'monthly_deduction']
        widgets = {
            'employee': forms.Select(attrs={'class': 'form-input'}),
            'loan_type': forms.Select(attrs={'class': 'form-input'}),
            'amount': forms.NumberInput(attrs={'class': 'form-input', 'step': '0.01'}),
            'balance': forms.NumberInput(attrs={'class': 'form-input', 'step': '0.01'}),
            'monthly_deduction': forms.NumberInput(attrs={'class': 'form-input', 'step': '0.01'}),
        }


class AllowanceForm(forms.ModelForm):
    class Meta:
        model = Allowance
        fields = ['employee', 'allowance_type', 'amount', 'deposit_date']
        widgets = {
            'employee': forms.Select(attrs={'class': 'form-input'}),
            'allowance_type': forms.Select(attrs={'class': 'form-input'}),
            'amount': forms.NumberInput(attrs={'class': 'form-input', 'step': '0.01'}),
            'deposit_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-input'}),
        }
