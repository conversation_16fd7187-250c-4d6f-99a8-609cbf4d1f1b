# Generated by Django 5.2.3 on 2025-07-17 09:08

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AllowanceType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('allowance_type', models.CharField(blank=True, max_length=100)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='LoanType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('loan_type', models.Char<PERSON>ield(blank=True, max_length=100)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Allowance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('deposit_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allowances', to=settings.AUTH_USER_MODEL)),
                ('allowance_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allowance_types', to='finance.allowancetype')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Loan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('balance', models.DecimalField(decimal_places=2, max_digits=10)),
                ('monthly_deduction', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loans', to=settings.AUTH_USER_MODEL)),
                ('loan_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loan_types', to='finance.loantype')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OJTPayslipData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cut_off', models.CharField(max_length=50)),
                ('regular_day', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('allowance_day', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('nd_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('grand_total', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('basic_school_share', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('basic_ojt_share', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('deduction', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('net_ojt_share', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('rice_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('ot_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('nd_ot_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('special_holiday', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('legal_holiday', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('satoff_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('rd_ot', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('adjustment', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('deduction_2', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('ot_pay_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_allow', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('line_number', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('holiday_hours', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('rd_ot_days', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('perfect_attendance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ojt_payslips', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Payslip',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cutoff_from', models.DateField()),
                ('cutoff_to', models.DateField()),
                ('file_path', models.FileField(blank=True, null=True, upload_to='payslips/')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('date_uploaded', models.DateTimeField(auto_now_add=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payslips', to=settings.AUTH_USER_MODEL)),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_payslips', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-cutoff_to'],
                'unique_together': {('employee', 'cutoff_from', 'cutoff_to')},
            },
        ),
    ]
