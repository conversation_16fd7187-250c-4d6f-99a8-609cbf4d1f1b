from django.db import models
from userlogin.models import EmployeeLogin

class Payslip(models.Model):
    employee = models.CharField(max_length=50, help_text="Employee ID number")  # Store idnumber directly
    cutoff_from = models.DateField()
    cutoff_to = models.DateField()
    file_path = models.FileField(upload_to='/payslip/', null=True, blank=True)
    date_uploaded = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(EmployeeLogin, on_delete=models.CASCADE, related_name='uploaded_payslips')

    class Meta:
        unique_together = ['employee', 'cutoff_from', 'cutoff_to']
        ordering = ['-cutoff_to']

    def __str__(self):
        return f"{self.employee} - {self.cutoff_from} to {self.cutoff_to}"

    @property
    def employee_obj(self):
        """Get the actual EmployeeLogin object"""
        try:
            return EmployeeLogin.objects.get(idnumber=self.employee)
        except EmployeeLogin.DoesNotExist:
            return None


class LoanType(models.Model):
    loan_type = models.CharField(max_length=100, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.loan_type


class AllowanceType(models.Model):
    allowance_type = models.CharField(max_length=100, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.allowance_type


class Loan(models.Model):
    employee = models.ForeignKey(EmployeeLogin, on_delete=models.CASCADE, related_name='loans')
    loan_type = models.ForeignKey(LoanType, on_delete=models.CASCADE, related_name='loan_types')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    balance = models.DecimalField(max_digits=10, decimal_places=2)
    monthly_deduction = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee.name} - {self.loan_type.loan_type}: {self.amount}"


class Allowance(models.Model):
    employee = models.ForeignKey(EmployeeLogin, on_delete=models.CASCADE, related_name='allowances')
    allowance_type = models.ForeignKey(AllowanceType, on_delete=models.CASCADE, related_name='allowance_types')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    deposit_date = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee.name} - {self.allowance_type.allowance_type}"


class OJTPayslipData(models.Model):
    employee = models.ForeignKey(EmployeeLogin, on_delete=models.CASCADE, related_name='ojt_payslips')
    cut_off = models.CharField(max_length=50)
    regular_day = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    allowance_day = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_allowance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    nd_allowance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    grand_total = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    basic_school_share = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    basic_ojt_share = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    deduction = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_ojt_share = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    rice_allowance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    ot_allowance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    nd_ot_allowance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    special_holiday = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    legal_holiday = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    satoff_allowance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    rd_ot = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    adjustment = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    deduction_2 = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    ot_pay_allowance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_allow = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    line_number = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    holiday_hours = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    rd_ot_days = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    perfect_attendance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"OJT {self.employee.name} - {self.cut_off}"
