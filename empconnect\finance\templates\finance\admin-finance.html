{% extends "main.html" %}
{% load static %}

{% block title %}REPConnect - Finance Management{% endblock title %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'css/finance/finance.css' %}">
{% endblock %}

{% block content %}
<div class="page-content" id="page-content">
    <header class="page-header">
        <div class="page-header-content">
            <h2>Financial Management</h2>
            <p>Uploading and managing employee payslips, loans, and allowances</p>
        </div>
    </header>
    <div class="dashboard-stats-container">
        <div class="dashboard-stats">
            <div class="modern-stats-grid">
                <div class="modern-stat-card">
                    <div class="modern-stat-header">
                        <div class="modern-stat-icon blue">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="modern-stat-label">Total Employees</div>
                    <div class="modern-stat-value">{{ total_employees }}</div>
                    <div class="modern-stat-change {% if employees_positive %}positive{% else %}negative{% endif %}">
                        <span class="modern-stat-change-icon">
                            <i class="fas {% if employees_positive %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                        </span>
                        {{ employees_percent }}%
                        <span class="modern-stat-change-text">vs prev. month</span>
                    </div>
                </div>
                <div class="modern-stat-card">
                    <div class="modern-stat-header">
                        <div class="modern-stat-icon orange">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                    </div>
                    <div class="modern-stat-label">Payslips</div>
                    <div class="modern-stat-value">{{ total_payslips }}</div>
                    <div class="modern-stat-change {% if payslips_positive %}positive{% else %}negative{% endif %}">
                        <span class="modern-stat-change-icon">
                            <i class="fas {% if payslips_positive %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                        </span>
                        {{ payslips_percent }}%
                        <span class="modern-stat-change-text">vs prev. month</span>
                    </div>
                </div>
                <div class="modern-stat-card">
                    <div class="modern-stat-header">
                        <div class="modern-stat-icon green">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                    </div>
                    <div class="modern-stat-label">Active Loans</div>
                    <div class="modern-stat-value">{{ total_loans }}</div>
                    <div class="modern-stat-change {% if loans_positive %}positive{% else %}negative{% endif %}">
                        <span class="modern-stat-change-icon">
                            <i class="fas {% if loans_positive %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                        </span>
                        {{ loans_percent }}%
                        <span class="modern-stat-change-text">vs prev. month</span>
                    </div>
                </div>
                <div class="modern-stat-card">
                    <div class="modern-stat-header">
                        <div class="modern-stat-icon red">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                    <div class="modern-stat-label">Allowances</div>
                    <div class="modern-stat-value">{{ total_allowances }}</div>
                    <div class="modern-stat-change {% if allowances_positive %}positive{% else %}negative{% endif %}">
                        <span class="modern-stat-change-icon">
                            <i class="fas {% if allowances_positive %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                        </span>
                        {{ allowances_percent }}%
                        <span class="modern-stat-change-text">vs prev. month</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="chart-card-container">
        <div class="chart-header">
            <h3>Finance Overview</h3>
            <div class="chart-controls">
                <div class="chart-filters">
                    <button class="filter-btn active" data-period="month">This Month</button>
                    <button class="filter-btn" data-period="quarter">This Quarter</button>
                    <button class="filter-btn" data-period="year">This Year</button>
                </div>
                
                <div class="chart-type-filters">
                    <button class="chart-type-btn active" data-type="line">
                        <i class="fas fa-chart-line"></i>
                    </button>
                    <button class="chart-type-btn" data-type="bar">
                        <i class="fas fa-chart-bar"></i>
                    </button>
                </div>
                <button id="filterBtn" class="btn btn-action" type="button">
                    <i class="fas fa-filter"></i> Filter
                </button>
                <div id="filterPopover" class="filter-popover">
                    <form class="filter-popover-content" onsubmit="applyFilter(event)">
                        <div class="filter-section">
                            <label class="filter-label">Filter By</label>
                            <select class="filter-field-select" id="filterCategorySelect" onchange="updateFilterOptions()">
                                <option value="">Select Category</option>
                                <option value="loans" selected>Loans</option>
                                <option value="allowances">Allowances</option>
                            </select>
                        </div>
                        
                        <div class="filter-section" id="filterTypeSection" style="display: none;">
                            <label class="filter-label">Type</label>
                            <div class="filter-type-options" id="filterTypeOptions">
                                <!-- Dynamic options will be loaded here -->
                            </div>
                        </div>
                        
                        <div class="filter-popover-actions">
                            <button type="button" class="btn btn-outline btn-sm" onclick="closeFilterPopover()">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary btn-sm">Apply Filter</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="financeChart"></canvas>
        </div>
    </div>

    <div class="component-card">
        <div class="table-actions table-actions-bar">
            <div class="table-actions-left" data-intro="Search for employees by name, ID number, or email" data-step="8">
                <form class="search-box" method="get" action="">
                    <input type="text" class="search-input" id="searchInput" placeholder="Search..." name="search" value="{{ search }}" />
                    <span class="search-icon"><i class="fas fa-search"></i></span>
                    {% if search %}
                    <span class="search-clear">
                        <a href="?"> <i class="fas fa-times"></i> </a>
                    </span>
                    {% endif %}
                </form>
            </div>
            <div class="table-actions-right" style="position:relative;">
                <button class="btn btn-action" id="importBtn">
                    <i class="fas fa-file-import"></i>
                    Import
                </button>
                <!-- Main Import Popover -->
                <div id="importPopover" class="import-popover">
                    <div class="import-popover-content">
                        <div class="import-option" data-type="payslips">
                            <i class="fas fa-share"></i>
                            <span>Payslips</span>
                            <i class="fas fa-chevron-right"></i>
                        </div>
                        <div class="import-option" data-type="loans">
                            <i class="fas fa-credit-card"></i>
                            <span>Loans</span>
                            <i class="fas fa-chevron-right"></i>
                        </div>
                        <div class="import-option" data-type="allowances">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>Allowances</span>
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- Secondary Import Options Popover (appears to the left) -->
                <div id="importOptionsPopover" class="import-options-popover">
                    <div class="import-options-content">
                        <!-- Payslips Options -->
                        <div class="import-options-group" data-group="payslips">
                            <div class="import-option-item">
                                <label class="radio-option">
                                    <input type="radio" name="import_type" value="payslips_regular" id="import_payslips_regular">
                                    <span class="radio-circle"></span>
                                    <span class="radio-label">Regular/Probationary</span>
                                </label>
                            </div>
                            <div class="import-option-item">
                                <label class="radio-option">
                                    <input type="radio" name="import_type" value="payslips_ojt" id="import_payslips_ojt">
                                    <span class="radio-circle"></span>
                                    <span class="radio-label">On Job Training</span>
                                </label>
                            </div>
                        </div>

                        <!-- Loans Options -->
                        <div class="import-options-group" data-group="loans" style="display: none;">
                            <div class="import-option-item">
                                <label class="radio-option">
                                    <input type="radio" name="import_type" value="loans_principal" id="import_loans_principal">
                                    <span class="radio-circle"></span>
                                    <span class="radio-label">Principal Balance</span>
                                </label>
                            </div>
                            <div class="import-option-item">
                                <label class="radio-option">
                                    <input type="radio" name="import_type" value="loans_deduction" id="import_loans_deduction">
                                    <span class="radio-circle"></span>
                                    <span class="radio-label">Deduction</span>
                                </label>
                            </div>
                        </div>

                        <!-- Allowances Options -->
                        <div class="import-options-group" data-group="allowances" style="display: none;">
                            <div class="import-option-item">
                                <label class="radio-option">
                                    <input type="radio" name="import_type" value="allowances" id="import_allowances">
                                    <span class="radio-circle"></span>
                                    <span class="radio-label">Allowances</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="tour-btn" onclick="startProductTour()">
                    <span class="tour-icon"><i class="fa-regular fa-circle-question"></i></span>
                    <span class="tour-tooltip">Page Tour</span>
                </button>
            </div>                                
        </div>
        <!-- Employee List Table -->
        <div class="employees-table-container" id="employeeTableContainer">
            {% load static %}
            <table class="data-table">
                <thead>
                    <tr>
                        <th></th>
                        <th>Id Number</th>
                        <th>Employee Name</th>
                        <th>Email</th>
                        <th>Department</th>
                        <th>Line</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                {% for emp in employee_page_obj %}
                    <tr>
                        <td>
                            <img src="{% if emp.avatar %}{{ emp.avatar.url }}{% else %}{% static 'images/profile/avatar.svg' %}{% endif %}" alt="Avatar" class="avatar">
                        </td>
                        <td>{{ emp.idnumber }}</td>
                        <td>{{ emp.firstname }} {{ emp.lastname }}</td>
                        <td>{{ emp.email }}</td>
                        <td>{% if emp.employment_info and emp.employment_info.department %}{{ emp.employment_info.department.department_name }}{% else %}-{% endif %}</td>
                        <td>{% if emp.employment_info and emp.employment_info.line %}{{ emp.employment_info.line }}{% else %}-{% endif %}</td>
                        <td>
                            <a href="{% url 'employee_finance_details' emp.id %}" class="btn btn-icon" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="7" class="table-no-data">
                            <div class="empty-icon"><i class="fas fa-search"></i></div>
                            <div class="table-no-data-title">No employees found</div>
                            <div class="table-no-data-desc">Try adjusting your search or filters.</div>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
            <div class="pagination" id="paginationContainer">
                <div class="pagination-info">
                    Showing <span id="startRecord">{{ employee_page_obj.start_index }}</span> to <span id="endRecord">{{ employee_page_obj.end_index }}</span> of <span id="totalRecords">{{ employee_page_obj.paginator.count }}</span> entries
                </div>
                <div class="pagination-controls" id="paginationControls">
                    {% if employee_page_obj.has_previous %}
                        <a class="pagination-btn" id="prevPage" href="?page={{ employee_page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% else %}
                        <span class="pagination-btn" id="prevPage" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </span>
                    {% endif %}
                    <div id="pageNumbers">
                        {% for num in employee_page_obj.paginator.page_range %}
                            {% if employee_page_obj.number == num %}
                                <span class="pagination-btn active">{{ num }}</span>
                            {% elif num > employee_page_obj.number|add:'-3' and num < employee_page_obj.number|add:'3' %}
                                <a class="pagination-btn" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}">{{ num }}</a>
                            {% endif %}
                        {% endfor %}
                    </div>
                    {% if employee_page_obj.has_next %}
                        <a class="pagination-btn" id="nextPage" href="?page={{ employee_page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% else %}
                        <span class="pagination-btn" id="nextPage" disabled>
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    {% endif %}
                </div>
            </div> 
        </div>
    </div>
</div>



<!-- Payslip Import Modal -->
<div id="payslipImportModal" class="modal modal-md">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">Import Payslips</h5>
            <button class="modal-close" id="closePayslipImportModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="tabs-horizontal" id="payslipImportTabs">
                <div class="tab-list">
                    <button class="tab active" data-tab="regular-probationary">Regular/Probationary</button>
                    <button class="tab" data-tab="ojt">OJT</button>
                </div>
            </div>

            <div class="tab-panel active" id="tab-regular-probationary">
                <div class="import-actions-header">
                    <p class="import-description">Upload payslip files for Regular/Probationary employees. Only PDF files are allowed.</p>
                </div>
                <div class="form-row" style="margin-bottom: 1rem; gap: 1rem; display: flex;">
                    <div class="form-group">
                        <label for="cutoff-start-date" class="form-label">Start Cut Off Date</label>
                        <input type="date" id="cutoff-start-date" name="cutoff_start_date" class="form-input">
                        <span class="field-error" id="cutoff-start-date-error"><span class="error-text"></span></span>
                    </div>
                    <div class="form-group">
                        <label for="cutoff-end-date" class="form-label">End Cut Off Date</label>
                        <input type="date" id="cutoff-end-date" name="cutoff_end_date" class="form-input">
                        <span class="field-error" id="cutoff-end-date-error"><span class="error-text"></span></span>
                    </div>
                </div>
                <div class="file-upload-area" id="payslip-upload-area-regular">
                    <div class="file-upload">
                        <input type="file" id="payslip-files-regular" name="payslip_files" multiple class="file-input" accept=".pdf">
                        <label for="payslip-files-regular" class="file-label">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Drag & drop PDF files here or click to browse</span>
                            <small>Supported format: .pdf (Max size: 10MB)</small>
                        </label>
                    </div>
                    <div class="selected-files" id="selectedPayslipFilesRegular" style="display: none;">
                        <h4>Selected Files:</h4>
                        <div class="file-list" id="payslip-file-list-regular"></div>
                    </div>
                    <div class="upload-progress" id="payslipUploadProgressRegular" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="payslipProgressFillRegular"></div>
                        </div>
                        <span class="progress-text" id="payslipProgressTextRegular">0%</span>
                    </div>
                </div>
            </div>
            <div class="tab-panel" id="tab-ojt">
                <div class="import-actions-header">
                    <p class="import-description">Upload payslip files for OJT employees. Only Excel or CSV files are allowed.</p>
                    <button class="btn btn-outline" id="ojtPayslipTemplateBtn">
                        <i class="fas fa-download"></i>
                        Download Template
                    </button>
                </div>
                <div class="file-upload-area" id="payslip-upload-area-ojt">
                    <div class="file-upload">
                        <input type="file" id="payslip-files-ojt" name="payslip_files" multiple class="file-input" accept=".xlsx,.xls,.csv">
                        <label for="payslip-files-ojt" class="file-label">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Drag & drop Excel/CSV files here or click to browse</span>
                            <small>Supported formats: .xlsx, .xls, .csv (Max size: 10MB)</small>
                        </label>
                    </div>
                    <div class="selected-files" id="selectedPayslipFilesOJT" style="display: none;">
                        <h4>Selected Files:</h4>
                        <div class="file-list" id="payslip-file-list-ojt"></div>
                    </div>
                    <div class="upload-progress" id="payslipUploadProgressOJT" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="payslipProgressFillOJT"></div>
                        </div>
                        <span class="progress-text" id="payslipProgressTextOJT">0%</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <form id="payslip-upload-form" action="/finance/payslips/upload/" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <button type="submit" class="btn btn-primary" id="payslip-upload-btn" disabled>
                    <i class="fas fa-upload"></i>
                    Upload Files
                </button>
            </form>
            <button class="btn btn-outline" id="cancelPayslipImportBtn">
                Cancel
            </button>
        </div>
    </div>
</div>

<!-- Loans Import Modal -->
<div id="loansImportModal" class="modal modal-md">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">Import Loans</h5>
            <button class="modal-close" id="closeLoansImportModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="import-actions-header">
                <p class="import-description">Upload loan files (XLSX, XLS, CSV). You can upload multiple files at once.</p>
                <button class="btn btn-outline" id="exportLoanTemplateBtn">
                    <i class="fas fa-download"></i>
                    Download Template
                </button>
            </div>

            <div class="file-upload-area" id="loans-upload-area">
                <div class="file-upload">
                    <input type="file" id="loans-files" name="files" multiple class="file-input" accept=".xlsx,.xls,.csv">
                    <label for="loans-files" class="file-label">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <span>Drag & drop loan files here or click to browse</span>
                        <small>Supported formats: .xlsx, .xls, .csv (Max size: 10MB)</small>
                    </label>
                </div>

                <div class="selected-files" id="selectedLoanFiles" style="display: none;">
                    <h4>Selected Files:</h4>
                    <div class="file-list" id="loans-file-list">
                    </div>
                </div>

                <div class="upload-progress" id="loanUploadProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="loanProgressFill"></div>
                    </div>
                    <span class="progress-text" id="loanProgressText">0%</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <form id="loans-upload-form" action="/finance/loans/upload/" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <button type="submit" class="btn btn-primary" id="loans-upload-btn" disabled>
                    <i class="fas fa-upload"></i>
                    Upload Files
                </button>
            </form>
            <button class="btn btn-outline" id="cancelLoanImportBtn">
                Cancel
            </button>
        </div>
    </div>
</div>

<!-- Allowances Import Modal -->
<div id="allowancesImportModal" class="modal modal-md">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">Import Allowances</h5>
            <button class="modal-close" id="closeAllowancesImportModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="import-actions-header">
                <p class="import-description">Upload allowance files (XLSX, XLS, CSV). You can upload multiple files at once.</p>
                <button class="btn btn-outline" id="exportAllowanceTemplateBtn">
                    <i class="fas fa-download"></i>
                    Download Template
                </button>
            </div>

            <div class="file-upload-area" id="allowances-upload-area">
                <div class="file-upload">
                    <input type="file" id="allowances-files" name="files" multiple class="file-input" accept=".xlsx,.xls,.csv">
                    <label for="allowances-files" class="file-label">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <span>Drag & drop allowance files here or click to browse</span>
                        <small>Supported formats: .xlsx, .xls, .csv (Max size: 10MB)</small>
                    </label>
                </div>

                <div class="selected-files" id="selectedAllowanceFiles" style="display: none;">
                    <h4>Selected Files:</h4>
                    <div class="file-list" id="allowances-file-list">
                    </div>
                </div>

                <div class="upload-progress" id="allowanceUploadProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="allowanceProgressFill"></div>
                    </div>
                    <span class="progress-text" id="allowanceProgressText">0%</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <form id="allowances-upload-form" action="/finance/allowances/upload/" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <button type="submit" class="btn btn-primary" id="allowances-upload-btn" disabled>
                    <i class="fas fa-upload"></i>
                    Upload Files
                </button>
            </form>
            <button class="btn btn-outline" id="cancelAllowanceImportBtn">
                Cancel
            </button>
        </div>
    </div>
</div>

<!-- Payslip Upload Results Modal -->
<div id="payslipResultsModal" class="modal modal-lg">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">Payslip Upload Results</h5>
            <button class="modal-close" id="closePayslipResultsModal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="results-table-container">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>Filename</th>
                            <th>Error</th>
                        </tr>
                    </thead>
                    <tbody id="failedUploadsTable">
                        <!-- Failed uploads will be populated here -->
                    </tbody>
                </table>
            </div>
            <div class="error-help">
                <h5>Common Issues:</h5>
                <ul>
                    <li><strong>Invalid filename format:</strong> Filename must follow pattern: lastname_employeeid.pdf</li>
                    <li><strong>Employee not found:</strong> Employee ID in filename doesn't exist in system</li>
                    <li><strong>Wrong employment type:</strong> Only Regular/Probationary employees are allowed</li>
                    <li><strong>Invalid file type:</strong> Only PDF files are accepted</li>
                </ul>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" id="downloadPayslipErrorReportBtn" style="display: none;">
                <i class="fas fa-download"></i>
                Download Error Report
            </button>
            <button class="btn btn-outline" id="closeResultsModalBtn">
                Close
            </button>
        </div>
    </div>
</div>

{% endblock content %}

{% block extra_js %}
<script src="{% static 'js/finance/admin-finance.js' %}"></script>
{% endblock %}