{% extends "main.html" %}
{% load static %}

{% block title %}REPConnect - Employee Finance Details{% endblock title %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'css/finance/finance.css' %}">
{% endblock %}

{% block content %}
<div class="page-content" id="page-content">
    <header class="page-header">
        <div class="page-header-content">
            <div class="page-header-left">
                <button class="btn btn-outline" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                    Back
                </button>
            </div>
            <div class="page-header-center">
                <h2>Employee Finance Details</h2>
                <p>Financial information for {{ employee.firstname }} {{ employee.lastname }}</p>
            </div>
        </div>
    </header>

    <div class="employee-info-card">
        <div class="employee-header">
            <div class="employee-avatar">
                <img src="{% if employee.avatar %}{{ employee.avatar.url }}{% else %}{% static 'images/profile/avatar.svg' %}{% endif %}" alt="Avatar" class="avatar">
            </div>
            <div class="employee-details">
                <h3>{{ employee.firstname }} {{ employee.lastname }}</h3>
                <p class="employee-id">ID: {{ employee.idnumber }}</p>
                <p class="employee-email">{{ employee.email }}</p>
                <div class="employee-department">
                    <span class="department-badge">
                        {% if employee.employment_info and employee.employment_info.department %}
                            {{ employee.employment_info.department.department_name }}
                        {% else %}
                            No Department
                        {% endif %}
                    </span>
                    {% if employee.employment_info and employee.employment_info.line %}
                        <span class="line-badge">Line {{ employee.employment_info.line }}</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="finance-sections">
        <!-- Payslips Section -->
        <div class="finance-section">
            <div class="section-header">
                <h3><i class="fas fa-file-invoice"></i> Payslips</h3>
                <span class="section-count">{{ payslips.count }} payslip{{ payslips.count|pluralize }}</span>
            </div>
            <div class="section-content">
                {% if payslips %}
                    <div class="payslips-grid">
                        {% for payslip in payslips %}
                            <div class="payslip-card">
                                <div class="card-header">
                                    <h4>{{ payslip.cutoff_from|date:"M d" }} - {{ payslip.cutoff_to|date:"M d, Y" }}</h4>
                                    <span class="payslip-type-badge {{ payslip.employee_type }}">{{ payslip.get_employee_type_display }}</span>
                                </div>
                                <div class="card-body">
                                    <div class="payslip-details">
                                        <div class="detail-row">
                                            <span class="label">Period:</span>
                                            <span class="value">{{ payslip.cutoff_from|date:"Y-m-d" }} to {{ payslip.cutoff_to|date:"Y-m-d" }}</span>
                                        </div>
                                        <div class="detail-row">
                                            <span class="label">Amount:</span>
                                            <span class="value highlight">₱{{ payslip.amount|floatformat:2 }}</span>
                                        </div>
                                        <div class="detail-row">
                                            <span class="label">Uploaded:</span>
                                            <span class="value">{{ payslip.date_uploaded|date:"M d, Y" }}</span>
                                        </div>
                                    </div>
                                </div>
                                {% if payslip.file_path %}
                                    <div class="card-footer">
                                        <a href="{{ payslip.file_path.url }}" class="btn btn-sm btn-outline" target="_blank">
                                            <i class="fas fa-download"></i>
                                            Download
                                        </a>
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state">
                        <div class="empty-icon"><i class="fas fa-file-invoice"></i></div>
                        <div class="empty-title">No Payslips Found</div>
                        <div class="empty-desc">No payslips have been uploaded for this employee yet.</div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Loans Section -->
        <div class="finance-section">
            <div class="section-header">
                <h3><i class="fas fa-hand-holding-usd"></i> Loans</h3>
                <span class="section-count">{{ loans.count }} loan{{ loans.count|pluralize }}</span>
            </div>
            <div class="section-content">
                {% if loans %}
                    <div class="loans-container">
                        {% regroup loans by loan_type as loan_groups %}
                        {% for group in loan_groups %}
                            <div class="loan-group">
                                <h4 class="group-title">{{ group.grouper.loan_type }} Loans</h4>
                                <div class="loans-table-container">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>Amount</th>
                                                <th>Balance</th>
                                                <th>Monthly Deduction</th>
                                                <th>Date Created</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for loan in group.list %}
                                                <tr>
                                                    <td>₱{{ loan.amount|floatformat:2 }}</td>
                                                    <td>₱{{ loan.balance|floatformat:2 }}</td>
                                                    <td>₱{{ loan.monthly_deduction|floatformat:2 }}</td>
                                                    <td>{{ loan.created_at|date:"M d, Y" }}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state">
                        <div class="empty-icon"><i class="fas fa-hand-holding-usd"></i></div>
                        <div class="empty-title">No Loans Found</div>
                        <div class="empty-desc">No loans have been recorded for this employee yet.</div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Allowances Section -->
        <div class="finance-section">
            <div class="section-header">
                <h3><i class="fas fa-coins"></i> Allowances</h3>
                <span class="section-count">{{ allowances.count }} allowance{{ allowances.count|pluralize }}</span>
            </div>
            <div class="section-content">
                {% if allowances %}
                    <div class="allowances-container">
                        {% regroup allowances by allowance_type as allowance_groups %}
                        {% for group in allowance_groups %}
                            <div class="allowance-group">
                                <h4 class="group-title">{{ group.grouper.allowance_type }} Allowances</h4>
                                <div class="allowances-table-container">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>Amount</th>
                                                <th>Deposit Date</th>
                                                <th>Date Created</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for allowance in group.list %}
                                                <tr>
                                                    <td>₱{{ allowance.amount|floatformat:2 }}</td>
                                                    <td>{{ allowance.deposit_date|date:"M d, Y" }}</td>
                                                    <td>{{ allowance.created_at|date:"M d, Y" }}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state">
                        <div class="empty-icon"><i class="fas fa-coins"></i></div>
                        <div class="empty-title">No Allowances Found</div>
                        <div class="empty-desc">No allowances have been recorded for this employee yet.</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block extra_js %}
<script src="{% static 'js/finance/employee-finance-details.js' %}"></script>
{% endblock %}
