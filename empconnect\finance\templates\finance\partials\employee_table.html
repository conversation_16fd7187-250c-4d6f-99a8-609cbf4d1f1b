{% load static %}
<table class="data-table">
    <thead>
        <tr>
            <th></th>
            <th>Id Number</th>
            <th>Employee Name</th>
            <th>Email</th>
            <th>Department</th>
            <th>Line</th>
            <th>Action</th>
        </tr>
    </thead>
    <tbody>
    {% for emp in employee_page_obj %}
        <tr>
            <td>
                <img src="{% if emp.avatar %}{{ emp.avatar.url }}{% else %}{% static 'images/profile/avatar.svg' %}{% endif %}" alt="Avatar" class="avatar">
            </td>
            <td>{{ emp.idnumber }}</td>
            <td>{{ emp.firstname }} {{ emp.lastname }}</td>
            <td>{{ emp.email }}</td>
            <td>{% if emp.employment_info and emp.employment_info.department %}{{ emp.employment_info.department.department_name }}{% else %}-{% endif %}</td>
            <td>{% if emp.employment_info and emp.employment_info.line %}{{ emp.employment_info.line }}{% else %}-{% endif %}</td>
            <td>
                <a href="{% url 'employee_finance_details' emp.id %}" class="btn btn-icon" title="View Details">
                    <i class="fas fa-eye"></i>
                </a>
            </td>
        </tr>
    {% empty %}
        <tr>
            <td colspan="7" class="table-no-data">
                <div class="empty-icon"><i class="fas fa-search"></i></div>
                <div class="table-no-data-title">No employees found</div>
                <div class="table-no-data-desc">Try adjusting your search or filters.</div>
            </td>
        </tr>
    {% endfor %}
    </tbody>
</table>
<!-- Pagination Bar -->
<div class="pagination" id="paginationContainer">
    <div class="pagination-info">
        Showing <span id="startRecord">{{ employee_page_obj.start_index }}</span> to <span id="endRecord">{{ employee_page_obj.end_index }}</span> of <span id="totalRecords">{{ employee_page_obj.paginator.count }}</span> entries
    </div>
    <div class="pagination-controls" id="paginationControls">
        {% if employee_page_obj.has_previous %}
            <a class="pagination-btn" id="prevPage" href="?page={{ employee_page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}">
                <i class="fas fa-chevron-left"></i>
            </a>
        {% else %}
            <span class="pagination-btn" id="prevPage" disabled>
                <i class="fas fa-chevron-left"></i>
            </span>
        {% endif %}
        <div id="pageNumbers">
            {% for num in employee_page_obj.paginator.page_range %}
                {% if employee_page_obj.number == num %}
                    <span class="pagination-btn active">{{ num }}</span>
                {% elif num > employee_page_obj.number|add:'-3' and num < employee_page_obj.number|add:'3' %}
                    <a class="pagination-btn" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}">{{ num }}</a>
                {% endif %}
            {% endfor %}
        </div>
        {% if employee_page_obj.has_next %}
            <a class="pagination-btn" id="nextPage" href="?page={{ employee_page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}">
                <i class="fas fa-chevron-right"></i>
            </a>
        {% else %}
            <span class="pagination-btn" id="nextPage" disabled>
                <i class="fas fa-chevron-right"></i>
            </span>
        {% endif %}
    </div>
</div> 