{% extends "main.html" %}
{% load static %}

{% block title %}REPConnect - Finance{% endblock title %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'css/finance/finance.css' %}">
{% endblock %}

{% block content %}
<div class="page-content" id="page-content">
    <div class="finance-header">
        <div class="header-content">
            <h2 class="page-title">My Financial Information</h2>
        </div>
    </div>

    <!-- Employee Interface -->
    <div class="finance-tabs">
        {% if employment_type == 'ojt' %}
        <!-- OJT Employee View -->
        <div class="nav-tabs">
            <button class="tab-btn active" data-tab="payslip-employee">Payslip</button>
        </div>

        <div id="payslip-employee" class="tab-content active">
            <div class="employee-section">
                <h3>My Payslips</h3>
                <div class="payslips-grid">
                    {% for payslip_data in ojt_payslips %}
                    <div class="payslip-card">
                        <div class="card-header">
                            <h4>{{ payslip_data.cut_off }}</h4>
                            <span class="payslip-type-badge ojt">OJT</span>
                        </div>
                        <div class="card-body">
                            <div class="payslip-details">
                                <div class="detail-row">
                                    <span class="label">Regular Days:</span>
                                    <span class="value">{{ payslip_data.regular_day }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">Allowance Days:</span>
                                    <span class="value">{{ payslip_data.allowance_day }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">Net OJT Share:</span>
                                    <span class="value highlight">{{ payslip_data.net_ojt_share }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary btn-sm send-payslip-btn" data-payslip-id="{{ payslip_data.id }}" data-payslip-type="ojt">
                                <i class="fas fa-envelope"></i>
                                Send to Email
                            </button>
                        </div>
                    </div>
                    {% empty %}
                    <div class="empty-state">
                        <i class="fas fa-file-invoice"></i>
                        <h4>No Payslips Available</h4>
                        <p>Your payslips will appear here once they are uploaded by the admin.</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        {% else %}
        <!-- Regular/Probationary Employee View -->
        <div class="nav-tabs">
            <button class="tab-btn active" data-tab="payslip-employee">Payslips</button>
            <button class="tab-btn" data-tab="loans-employee">Loans</button>
            <button class="tab-btn" data-tab="allowances-employee">Allowances</button>
        </div>

        <!-- Employee Payslip Tab -->
        <div id="payslip-employee" class="tab-content active">
            <div class="employee-section">
                <h3>My Payslips</h3>
                <div class="payslips-grid">
                    {% for payslip in payslips %}
                    <div class="payslip-card">
                        <div class="card-header">
                            <h4>{{ payslip.cutoff_from|date:"M d" }} - {{ payslip.cutoff_to|date:"M d, Y" }}</h4>
                            <span class="payslip-type-badge {{ payslip.employee_type }}">{{ payslip.get_employee_type_display }}</span>
                        </div>
                        <div class="card-body">
                            <div class="payslip-details">
                                <div class="detail-row">
                                    <span class="label">Period:</span>
                                    <span class="value">{{ payslip.cutoff_from }} to {{ payslip.cutoff_to }}</span>
                                </div>
                                {% if payslip.amount %}
                                <div class="detail-row">
                                    <span class="label">Amount:</span>
                                    <span class="value highlight">₱{{ payslip.amount|floatformat:2 }}</span>
                                </div>
                                {% endif %}
                                <div class="detail-row">
                                    <span class="label">Uploaded:</span>
                                    <span class="value">{{ payslip.date_uploaded|date:"M d, Y" }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary btn-sm send-payslip-btn" data-payslip-id="{{ payslip.id }}" data-payslip-type="regular">
                                <i class="fas fa-envelope"></i>
                                Send to Email
                            </button>
                        </div>
                    </div>
                    {% empty %}
                    <div class="empty-state">
                        <i class="fas fa-file-invoice"></i>
                        <h4>No Payslips Available</h4>
                        <p>Your payslips will appear here once they are uploaded by the admin.</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Employee Loans Tab -->
        <div id="loans-employee" class="tab-content">
            <div class="employee-section">
                <h3>My Loans</h3>
                {% if loans %}
                    {% regroup loans by loan_type as loan_groups %}
                    <div class="loans-container">
                        {% for loan_group in loan_groups %}
                        <div class="loan-group">
                            <h4 class="group-title">{{ loan_group.grouper|title }} Loans</h4>
                            <div class="loans-table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Amount</th>
                                            <th>Balance</th>
                                            <th>Monthly Deduction</th>
                                            <th>Date Issued</th>
                                            <th>Terms</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for loan in loan_group.list %}
                                        <tr>
                                            <td>₱{{ loan.amount|floatformat:2 }}</td>
                                            <td>₱{{ loan.balance|floatformat:2 }}</td>
                                            <td>₱{{ loan.monthly_deduction|floatformat:2 }}</td>
                                            <td>{{ loan.date_issued|date:"M d, Y" }}</td>
                                            <td>{{ loan.terms_months }} months</td>
                                            <td>
                                                <span class="status-badge {{ loan.status }}">{{ loan.get_status_display }}</span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                <div class="empty-state">
                    <i class="fas fa-hand-holding-usd"></i>
                    <h4>No Loans Found</h4>
                    <p>You currently have no loans in the system.</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Employee Allowances Tab -->
        <div id="allowances-employee" class="tab-content">
            <div class="employee-section">
                <h3>My Allowances</h3>
                {% if allowances %}
                    {% regroup allowances by allowance_type as allowance_groups %}
                    <div class="allowances-container">
                        {% for allowance_group in allowance_groups %}
                        <div class="allowance-group">
                            <h4 class="group-title">{{ allowance_group.grouper|title }} Allowances</h4>
                            <div class="allowances-table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Amount</th>
                                            <th>Frequency</th>
                                            <th>Start Date</th>
                                            <th>End Date</th>
                                            <th>Status</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for allowance in allowance_group.list %}
                                        <tr>
                                            <td>₱{{ allowance.amount|floatformat:2 }}</td>
                                            <td>{{ allowance.get_frequency_display }}</td>
                                            <td>{{ allowance.start_date|date:"M d, Y" }}</td>
                                            <td>{% if allowance.end_date %}{{ allowance.end_date|date:"M d, Y" }}{% else %}-{% endif %}</td>
                                            <td>
                                                <span class="status-badge {% if allowance.is_active %}active{% else %}inactive{% endif %}">
                                                    {% if allowance.is_active %}Active{% else %}Inactive{% endif %}
                                                </span>
                                            </td>
                                            <td>{{ allowance.description|default:"-" }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                <div class="empty-state">
                    <i class="fas fa-coins"></i>
                    <h4>No Allowances Found</h4>
                    <p>You currently have no allowances in the system.</p>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Email Selection Modal -->
<div id="emailSelectionModal" class="modal">
    <div class="modal-overlay"></div>
    <div class="modal-content modal-sm">
        <div class="modal-header">
            <h3>Select Email Address</h3>
            <button class="modal-close" onclick="employeeFinance.closeModal('emailSelectionModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="email-selection-form">
                <form id="email-selection-form">
                    {% csrf_token %}
                    <div class="form-group">
                        <label class="form-label">Choose where to send your payslip:</label>
                        <div class="radio-input" id="email-options">
                            <!-- Dynamic radio options will be loaded here -->
                        </div>
                    </div>
                    
                    <div class="payslip-info" id="payslip-info" style="display: none;">
                        <h4>Payslip Details</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="label">Employee:</span>
                                <span class="value" id="payslip-employee-name">-</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Period:</span>
                                <span class="value" id="payslip-period">-</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Type:</span>
                                <span class="value" id="payslip-type">-</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" id="confirm-email-send">
                <i class="fas fa-envelope"></i>
                Send Payslip
            </button>
            <button class="btn btn-outline" onclick="employeeFinance.closeModal('emailSelectionModal')">Cancel</button>
        </div>
    </div>
</div>

<style>
/* Email selection modal specific styles */
.email-selection-form {
    padding: var(--space-md);
}

.payslip-info {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    margin: var(--space-lg) 0;
    border: 1px solid var(--border-color);
}

.payslip-info h4 {
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    padding-bottom: var(--space-sm);
    border-bottom: 2px solid var(--primary-color);
}

.info-grid {
    display: grid;
    gap: var(--space-sm);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-sm) 0;
}

.info-item .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.info-item .value {
    color: var(--text-primary);
    font-weight: 600;
}

.radio-input {
    margin: var(--space-md) 0;
}

.radio-input .label {
    display: flex;
    align-items: center;
    padding: var(--space-md);
    margin-bottom: var(--space-sm);
    background: var(--surface);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.radio-input .label:hover {
    border-color: var(--primary-color);
    background: var(--surface-hover);
}

.radio-input .label input[type="radio"] {
    margin-right: var(--space-md);
}

.radio-input .label input[type="radio"]:checked + .text {
    color: var(--primary-color);
    font-weight: 600;
}

.radio-input .label .text {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.loading-state {
    text-align: center;
    padding: var(--space-xl);
    color: var(--text-muted);
}

.loading-state .loading-spinner {
    margin: 0 auto var(--space-md) auto;
}
</style>
{% endblock content %}

{% block extra_js %}
    <script src="{% static 'js/finance/user-finance.js' %}"></script>
{% endblock %}