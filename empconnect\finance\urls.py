from django.urls import path
from . import views

urlpatterns = [
    path('', views.user_finance, name='user_finance'),
    path('admin/', views.finance_dashboard, name='admin_finance'),
    path('payslip/upload/', views.payslip_upload, name='payslip_upload'),
    path('payslips/upload/', views.upload_payslips, name='upload_payslips'),
    path('payslips/upload-progress/<str:upload_id>/', views.payslip_upload_progress, name='payslip_upload_progress'),
    path('payslip/send/<int:payslip_id>/', views.send_payslip, name='send_payslip'),
    path('loans/upload/', views.loans_upload, name='loans_upload'),
    path('allowances/upload/', views.allowances_upload, name='allowances_upload'),
    path('employees/', views.employees_list, name='employees_list'),
    path('employee/<int:employee_id>/loans/', views.employee_loans, name='employee_loans'),
    path('employee/<int:employee_id>/allowances/', views.employee_allowances, name='employee_allowances'),
    path('employee/<int:employee_id>/details/', views.employee_finance_details, name='employee_finance_details'),
    path('chart-data/', views.chart_data, name='chart_data'),
    path('filter-options/', views.filter_options, name='filter_options'),
    path('employee-table/', views.employee_table_partial, name='employee_table_partial'),
    path('employee-details/<int:employee_id>/', views.employee_details, name='employee_details'),
    path('template/ojt_payslip/', views.ojt_payslip_template, name='ojt_payslip_template'),
]