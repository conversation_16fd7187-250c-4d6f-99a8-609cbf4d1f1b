from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.mail import EmailMessage
from django.conf import settings
from django.db.models import Q
from django.core.paginator import Paginator
from django.utils import timezone
from userlogin.models import EmployeeLogin
import os
import pandas as pd
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import io
import openpyxl
from openpyxl.styles import Font, PatternFill
from io import BytesIO
from .models import Payslip, Loan, Allowance, OJTPayslipData, AllowanceType, LoanType
from .forms import PayslipUploadForm, EmployeeSearchForm, EmailSelectionForm
from django.template.loader import render_to_string
from django.views.decorators.http import require_GET
import csv
from django.views.decorators.csrf import csrf_exempt
from userlogin.models import EmployeeLogin
from .models import Payslip
import re
import threading
import time
from datetime import datetime
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile

@login_required
def finance_dashboard(request):
    user = request.user
    context = {}

    # Determine user type and role
    if hasattr(user, 'personalinformation'):
        employment_type = getattr(user.personalinformation, 'employment_type', 'regular')
    else:
        employment_type = 'regular'

    is_admin = user.accounting_admin

    if is_admin:
        # Admin view
        from django.utils import timezone
        from datetime import timedelta
        now = timezone.now()
        first_of_this_month = now.replace(day=1)
        first_of_last_month = (first_of_this_month - timedelta(days=1)).replace(day=1)
        last_of_last_month = first_of_this_month - timedelta(days=1)

        # Employees
        employees_this_month = EmployeeLogin.objects.filter(is_active=True, date_joined__gte=first_of_this_month).count()
        employees_last_month = EmployeeLogin.objects.filter(is_active=True, date_joined__gte=first_of_last_month, date_joined__lt=first_of_this_month).count()
        total_employees = EmployeeLogin.objects.filter(is_active=True).count()
        employees_percent = 0
        employees_positive = True
        if employees_last_month:
            employees_percent = round(((employees_this_month - employees_last_month) / employees_last_month) * 100, 1)
            employees_percent = min(employees_percent, 100)
            employees_positive = employees_this_month >= employees_last_month

        # Employee list for table (exclude current user and all admin flags)
        employee_list = EmployeeLogin.objects.filter(
            is_active=True
        ).exclude(
            id=request.user.id
        ).exclude(
            wire_admin=True
        ).exclude(
            clinic_admin=True
        ).exclude(
            iad_admin=True
        ).exclude(
            accounting_admin=True
        ).exclude(
            hr_admin=True
        ).exclude(
            hr_manager=True
        ).exclude(
            mis_admin=True
        )

        # Search filter
        search = request.GET.get('search', '').strip()
        if search:
            employee_list = employee_list.filter(
                Q(idnumber__icontains=search) |
                Q(firstname__icontains=search) |
                Q(lastname__icontains=search) |
                Q(username__icontains=search) |
                Q(email__icontains=search)
            )

        employee_list = employee_list.order_by('lastname', 'firstname')

        # Paginate employee list (10 per page)
        paginator = Paginator(employee_list, 10)
        page_number = request.GET.get('page')
        employee_page_obj = paginator.get_page(page_number)

        # Payslips
        payslips_this_month = Payslip.objects.filter(cutoff_to__gte=first_of_this_month).count()
        payslips_last_month = Payslip.objects.filter(cutoff_to__gte=first_of_last_month, cutoff_to__lt=first_of_this_month).count()
        total_payslips = Payslip.objects.count()
        payslips_percent = 0
        payslips_positive = True
        if payslips_last_month:
            payslips_percent = round(((payslips_this_month - payslips_last_month) / payslips_last_month) * 100, 1)
            payslips_percent = min(payslips_percent, 100)
            payslips_positive = payslips_this_month >= payslips_last_month

        # Loans
        loans_this_month = Loan.objects.filter(created_at__gte=first_of_this_month).count()
        loans_last_month = Loan.objects.filter(created_at__gte=first_of_last_month, created_at__lt=first_of_this_month).count()
        total_loans = Loan.objects.count()
        loans_percent = 0
        loans_positive = True
        if loans_last_month:
            loans_percent = round(((loans_this_month - loans_last_month) / loans_last_month) * 100, 1)
            loans_percent = min(loans_percent, 100)
            loans_positive = loans_this_month >= loans_last_month

        # Allowances
        allowances_this_month = Allowance.objects.filter(created_at__gte=first_of_this_month).count()
        allowances_last_month = Allowance.objects.filter(created_at__gte=first_of_last_month, created_at__lt=first_of_this_month).count()
        total_allowances = Allowance.objects.count()
        allowances_percent = 0
        allowances_positive = True
        if allowances_last_month:
            allowances_percent = round(((allowances_this_month - allowances_last_month) / allowances_last_month) * 100, 1)
            allowances_percent = min(allowances_percent, 100)
            allowances_positive = allowances_this_month >= allowances_last_month

        context.update({
            'is_admin': True,
            'total_employees': total_employees,
            'employees_percent': employees_percent,
            'employees_positive': employees_positive,
            'total_payslips': total_payslips,
            'payslips_percent': payslips_percent,
            'payslips_positive': payslips_positive,
            'total_loans': total_loans,
            'loans_percent': loans_percent,
            'loans_positive': loans_positive,
            'total_allowances': total_allowances,
            'allowances_percent': allowances_percent,
            'allowances_positive': allowances_positive,
            'employee_list': employee_page_obj,
            'employee_page_obj': employee_page_obj,
            'search': search,
        })
    else:
        # Employee view
        context.update({
            'is_admin': False,
            'employment_type': employment_type,
        })

        if employment_type == 'ojt':
            context['ojt_payslips'] = OJTPayslipData.objects.filter(employee=user).order_by('-created_at')
        else:
            context.update({
                'payslips': Payslip.objects.filter(employee=user).order_by('-cutoff_to'),
                'loans': Loan.objects.filter(employee=user).order_by('-date_issued'),
                'allowances': Allowance.objects.filter(employee=user).order_by('-start_date'),
            })

    return render(request, 'finance/admin-finance.html', context)

@login_required
def user_finance(request):
    user = request.user
    if hasattr(user, 'personalinformation'):
        employment_type = getattr(user.personalinformation, 'employment_type', 'regular')
    else:
        employment_type = 'regular'

    content = {
        'employment_type':employment_type,
    }
    return render(request, 'finance/user-finance.html', content)

@csrf_exempt
def payslip_upload(request):
    if request.method == 'POST':
        files = request.FILES.getlist('files')
        errors = []
        created = 0
        for f in files:
            print('DEBUG: Processing file:', f.name)
            try:
                base = f.name.rsplit('.', 1)[0]
                parts = base.split('_')
                print('DEBUG: Filename parts:', parts)
                if len(parts) < 2:
                    errors.append(f'Invalid filename: {f.name}')
                    continue
                idnumber = parts[-1]
                print('DEBUG: Extracted idnumber:', idnumber)
                employee = EmployeeLogin.objects.filter(idnumber=idnumber).first()
                print('DEBUG: Employee found:', employee)
                if not employee:
                    errors.append(f'No employee with idnumber {idnumber} for file {f.name}')
                    continue
                payslip = Payslip(employee=employee, file_path=f)
                payslip.save()
                created += 1
            except Exception as e:
                errors.append(f'Error with file {f.name}: {str(e)}')
        if errors:
            return JsonResponse({'success': False, 'errors': errors, 'created': created})
        return JsonResponse({'success': True, 'created': created})
    return JsonResponse({'success': False, 'error': 'Invalid request'}, status=400)

# Global variable to store upload progress
payslip_upload_progress_data = {}

@login_required
def upload_payslips(request):
    """Handle payslip file upload with validation and progress tracking"""
    if not request.user.accounting_admin:
        return JsonResponse({'success': False, 'message': 'Permission denied'})

    if request.method == 'POST':
        files = request.FILES.getlist('payslip_files')
        cutoff_from = request.POST.get('cutoff_from')
        cutoff_to = request.POST.get('cutoff_to')

        if not files:
            return JsonResponse({'success': False, 'message': 'No files selected'})

        if not cutoff_from or not cutoff_to:
            return JsonResponse({'success': False, 'message': 'Cutoff dates are required'})

        # Generate unique upload ID
        import uuid
        upload_id = str(uuid.uuid4())

        # Initialize progress tracking
        payslip_upload_progress_data[upload_id] = {
            'status': 'processing',
            'total': len(files),
            'processed': 0,
            'success_count': 0,
            'error_count': 0,
            'errors': [],
            'successful_uploads': []
        }

        # Start background processing
        thread = threading.Thread(
            target=process_payslip_upload,
            args=(upload_id, files, cutoff_from, cutoff_to, request.user)
        )
        thread.daemon = True
        thread.start()

        return JsonResponse({
            'success': True,
            'upload_id': upload_id,
            'message': 'Upload started'
        })

    return JsonResponse({'success': False, 'message': 'Invalid request method'})

def process_payslip_upload(upload_id, files, cutoff_from, cutoff_to, uploaded_by):
    """Process payslip files in background thread"""
    try:
        progress = payslip_upload_progress_data[upload_id]

        # Parse cutoff dates
        from datetime import datetime
        cutoff_from_date = datetime.strptime(cutoff_from, '%Y-%m-%d').date()
        cutoff_to_date = datetime.strptime(cutoff_to, '%Y-%m-%d').date()

        # Generate cutoff prefix for filename (mm-dd-yy format)
        cutoff_prefix = cutoff_to_date.strftime('%m-%d-%y')

        for file in files:
            try:
                # Validate file type
                if not file.name.lower().endswith('.pdf'):
                    progress['errors'].append({
                        'filename': file.name,
                        'error': 'Only PDF files are allowed'
                    })
                    progress['error_count'] += 1
                    progress['processed'] += 1
                    continue

                # Parse filename using regex
                filename_pattern = r'^([A-Za-z]+)_([A-Za-z0-9]+)\.pdf$'
                match = re.match(filename_pattern, file.name)

                if not match:
                    progress['errors'].append({
                        'filename': file.name,
                        'error': 'Invalid filename format. Expected: lastname_employeeid.pdf'
                    })
                    progress['error_count'] += 1
                    progress['processed'] += 1
                    continue

                lastname, employee_id = match.groups()

                # Validate employee exists
                try:
                    employee = EmployeeLogin.objects.get(idnumber=employee_id)

                    # Check if employee is regular or probationary
                    emp_info = getattr(employee, 'employment_info', None)
                    if emp_info and emp_info.employment_type not in ['Regular', 'Probationary']:
                        progress['errors'].append({
                            'filename': file.name,
                            'error': f'Employee {employee_id} is not regular/probationary (Type: {emp_info.employment_type})'
                        })
                        progress['error_count'] += 1
                        progress['processed'] += 1
                        continue

                except EmployeeLogin.DoesNotExist:
                    progress['errors'].append({
                        'filename': file.name,
                        'error': f'Employee with ID {employee_id} not found'
                    })
                    progress['error_count'] += 1
                    progress['processed'] += 1
                    continue

                # Generate final filename with cutoff dates
                final_filename = f"{cutoff_prefix}_{file.name}"

                # Save file to static/images/payslip/
                file_path = f"static/images/payslip/{final_filename}"

                # Ensure directory exists
                os.makedirs(os.path.dirname(file_path), exist_ok=True)

                # Save file
                with open(file_path, 'wb+') as destination:
                    for chunk in file.chunks():
                        destination.write(chunk)

                # Create or update payslip record
                payslip, created = Payslip.objects.update_or_create(
                    employee=employee_id,
                    cutoff_from=cutoff_from_date,
                    cutoff_to=cutoff_to_date,
                    defaults={
                        'file_path': file_path,
                        'uploaded_by': uploaded_by
                    }
                )

                progress['successful_uploads'].append({
                    'filename': file.name,
                    'employee_id': employee_id,
                    'employee_name': f"{employee.firstname} {employee.lastname}",
                    'final_filename': final_filename,
                    'action': 'Created' if created else 'Updated'
                })
                progress['success_count'] += 1

            except Exception as e:
                progress['errors'].append({
                    'filename': file.name,
                    'error': f'Processing error: {str(e)}'
                })
                progress['error_count'] += 1

            progress['processed'] += 1
            time.sleep(0.1)  # Small delay for progress visualization

        progress['status'] = 'completed'

    except Exception as e:
        progress['status'] = 'failed'
        progress['error_message'] = str(e)

@login_required
def payslip_upload_progress(request, upload_id):
    """Get upload progress status"""
    if not request.user.accounting_admin:
        return JsonResponse({'success': False, 'message': 'Permission denied'})

    progress = payslip_upload_progress_data.get(upload_id, {
        'status': 'not_found',
        'total': 0,
        'processed': 0,
        'success_count': 0,
        'error_count': 0,
        'errors': []
    })

    return JsonResponse(progress)

def process_ojt_excel(file, cutoff_from, cutoff_to, uploaded_by):
    try:
        df = pd.read_excel(file)
        success = True
        errors = []

        for index, row in df.iterrows():
            try:
                idnumber = str(row.get('ID_NO', '')).strip()
                if not idnumber:
                    continue

                try:
                    employee = EmployeeLogin.objects.get(idnumber=idnumber)
                except EmployeeLogin.DoesNotExist:
                    errors.append(f"Employee not found for ID: {idnumber}")
                    continue

                # Check for duplicate
                if OJTPayslipData.objects.filter(
                    employee=employee,
                    cut_off=row.get('Cut_Off', '')
                ).exists():
                    errors.append(f"OJT payslip already exists for {employee.name} for {row.get('Cut_Off', '')}")
                    continue

                OJTPayslipData.objects.create(
                    employee=employee,
                    cut_off=str(row.get('Cut_Off', '')),
                    regular_day=float(row.get('Regular_Day', 0) or 0),
                    allowance_day=float(row.get('ALLOWANCE_DAY', 0) or 0),
                    total_allowance=str(row.get('TOTAL_ALLOWANCE', '')),
                    nd_allowance=str(row.get('ND_ALLOWANCE', '')),
                    grand_total=str(row.get('GRAND_TOTAL', '')),
                    basic_school_share=str(row.get('BASIC_SCHOOL_SHARE', '')),
                    basic_ojt_share=str(row.get('BASIC_OJT_SHARE', '')),
                    deduction=float(row.get('DEDUCTION', 0) or 0),
                    net_ojt_share=str(row.get('NET_OJT_SHARE', '')),
                    rice_allowance=float(row.get('RICE_ALLOWANCE', 0) or 0),
                    ot_allowance=str(row.get('OT_ALLOWANCE', '')),
                    nd_ot_allowance=str(row.get('ND_OT_ALLOWANCE', '')),
                    special_holiday=float(row.get('SPECIAL_HOLIDAY', 0) or 0),
                    legal_holiday=float(row.get('LEGAL_HOLIDAY', 0) or 0),
                    satoff_allowance=float(row.get('SATOFF_ALLOWANCE', 0) or 0),
                    rd_ot=str(row.get('RD_OT', '')),
                    adjustment=str(row.get('ADJUSTMENT', '')),
                    deduction_2=float(row.get('DEDUCTION_2', 0) or 0),
                    ot_pay_allowance=str(row.get('OT_PAY_ALLOWANCE', '')),
                    total_allow=str(row.get('TOTAL_ALLOW', '')),
                    line=float(row.get('LINE', 0) or 0),
                    holiday_date=float(row.get('HOLIDAY_DATE', 0) or 0),
                    rd_ot_date=float(row.get('RD_OT_DATE', 0) or 0),
                    perfect_attendance=float(row.get('Perfect_Attendance', 0) or 0),
                )

            except Exception as e:
                errors.append(f"Error processing row {index + 2}: {str(e)}")
                success = False

        return success, errors

    except Exception as e:
        return False, [f"Error reading Excel file: {str(e)}"]

@login_required
def send_payslip(request, payslip_id):
    payslip = get_object_or_404(Payslip, id=payslip_id)

    # Check permissions
    if not (request.user == payslip.employee or
            request.user.accounting_admin):
        return JsonResponse({'success': False, 'message': 'Permission denied'})

    if request.method == 'POST':
        form = EmailSelectionForm(payslip.employee, request.POST)
        if form.is_valid():
            email_type = form.cleaned_data['email_type']

            if email_type == 'personal':
                email = payslip.employee.email
            else:
                email = getattr(payslip.employee, 'work_email', '') if hasattr(payslip.employee, 'work_email') else ''

            if not email:
                return JsonResponse({'success': False, 'message': 'Email address not found'})

            try:
                # Send email with payslip
                subject = f"Payslip for {payslip.cutoff_from} to {payslip.cutoff_to}"
                message = f"Dear {payslip.employee.name},\n\nPlease find attached your payslip for the period {payslip.cutoff_from} to {payslip.cutoff_to}.\n\nBest regards,\nHR Department"

                email_message = EmailMessage(subject, message, settings.DEFAULT_FROM_EMAIL, [email])

                if payslip.file_path:
                    email_message.attach_file(payslip.file_path.path)

                email_message.send()

                return JsonResponse({'success': True, 'message': f'Payslip sent to {email}'})
            except Exception as e:
                return JsonResponse({'success': False, 'message': f'Error sending email: {str(e)}'})

    # Return form for email selection
    form = EmailSelectionForm(payslip.employee)
    return render(request, 'finance/email_selection.html', {'form': form, 'payslip': payslip})

@login_required
def employee_loans(request, employee_id):
    if not request.user.accounting_admin:
        return JsonResponse({'success': False, 'message': 'Permission denied'})

    employee = get_object_or_404(EmployeeLogin, id=employee_id)
    loans = Loan.objects.filter(employee=employee).order_by('-created_at')

    # Group loans by type
    loan_groups = {}
    for loan in loans:
        loan_type = loan.loan_type.loan_type
        if loan_type not in loan_groups:
            loan_groups[loan_type] = []
        loan_groups[loan_type].append({
            'id': loan.id,
            'amount': str(loan.amount),
            'balance': str(loan.balance),
            'monthly_deduction': str(loan.monthly_deduction),
            'date_created': loan.created_at.strftime('%Y-%m-%d'),
            'loan_type': loan.loan_type.loan_type,
        })

    return JsonResponse({
        'success': True,
        'employee': {
            'name': f"{employee.firstname} {employee.lastname}",
            'idnumber': employee.idnumber,
        },
        'loan_groups': loan_groups
    })

@login_required
def employee_allowances(request, employee_id):
    if not request.user.accounting_admin:
        return JsonResponse({'success': False, 'message': 'Permission denied'})

    employee = get_object_or_404(EmployeeLogin, id=employee_id)
    allowances = Allowance.objects.filter(employee=employee).order_by('-start_date')

    # Group allowances by type
    allowance_groups = {}
    for allowance in allowances:
        allowance_type = allowance.get_allowance_type_display()
        if allowance_type not in allowance_groups:
            allowance_groups[allowance_type] = []
        allowance_groups[allowance_type].append({
            'id': allowance.id,
            'amount': str(allowance.amount),
            'frequency': allowance.get_frequency_display(),
            'start_date': allowance.start_date.strftime('%Y-%m-%d'),
            'end_date': allowance.end_date.strftime('%Y-%m-%d') if allowance.end_date else None,
            'is_active': allowance.is_active,
            'description': allowance.description,
        })

    return JsonResponse({
        'success': True,
        'employee': {
            'name': employee.name,
            'idnumber': employee.idnumber,
        },
        'allowance_groups': allowance_groups
    })

@login_required
def employees_list(request):
    if not request.user.accounting_admin:
        return JsonResponse({'success': False, 'message': 'Permission denied'})

    search_form = EmployeeSearchForm(request.GET)
    tab = request.GET.get('tab')
    if tab == 'loans':
        employees = EmployeeLogin.objects.filter(
            is_active=True,
            wire_admin=False,
            clinic_admin=False,
            iad_admin=False,
            accounting_admin=False,
            hr_admin=False,
            hr_manager=False,
            mis_admin=False,
            loans__isnull=False
        ).distinct().order_by('lastname', 'firstname')
    else:
        employees = EmployeeLogin.objects.filter(
            is_active=True
        ).order_by('lastname', 'firstname')

    if search_form.is_valid():
        search = search_form.cleaned_data.get('search')
        department = search_form.cleaned_data.get('department')
        employment_type = search_form.cleaned_data.get('employment_type')

        if search:
            employees = employees.filter(
                Q(firstname__icontains=search) |
                Q(lastname__icontains=search) |
                Q(idnumber__icontains=search) |
                Q(username__icontains=search)
            )

        if department:
            employees = employees.filter(
                employment_info__department__name__icontains=department
            )

        if employment_type:
            employees = employees.filter(
                employment_info__employment_type=employment_type
            )

    paginator = Paginator(employees, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    employees_data = []
    for employee in page_obj:
        emp_info = getattr(employee, 'employment_info', None)
        department = getattr(emp_info.department, 'name', '-') if emp_info and emp_info.department else '-'
        emp_type = getattr(emp_info, 'employment_type', 'regular') if emp_info else 'regular'

        employees_data.append({
            'id': employee.id,
            'name': f"{employee.firstname or ''} {employee.lastname or ''}".strip() or employee.username or 'Unknown',
            'idnumber': employee.idnumber,
            'department': department,
            'employment_type': emp_type.title(),
            'email': employee.email,
        })

    return JsonResponse({
        'success': True,
        'employees': employees_data,
        'has_next': page_obj.has_next(),
        'has_previous': page_obj.has_previous(),
        'page_number': page_obj.number,
        'total_pages': paginator.num_pages,
        'total_count': paginator.count,
    })

@login_required
@require_GET
def employee_table_partial(request):
    user = request.user
    employee_list = EmployeeLogin.objects.filter(
        is_active=True
    ).exclude(
        id=user.id
    ).exclude(
        wire_admin=True
    ).exclude(
        clinic_admin=True
    ).exclude(
        iad_admin=True
    ).exclude(
        accounting_admin=True
    ).exclude(
        hr_admin=True
    ).exclude(
        hr_manager=True
    ).exclude(
        mis_admin=True
    )

    search = request.GET.get('search', '').strip()
    if search:
        employee_list = employee_list.filter(
            Q(idnumber__icontains=search) |
            Q(firstname__icontains=search) |
            Q(lastname__icontains=search) |
            Q(username__icontains=search) |
            Q(email__icontains=search)
        )

    employee_list = employee_list.order_by('lastname', 'firstname')
    paginator = Paginator(employee_list, 10)
    page_number = request.GET.get('page')
    employee_page_obj = paginator.get_page(page_number)

    html = render_to_string('finance/partials/employee_table.html', {
        'employee_page_obj': employee_page_obj,
        'search': search,
    }, request=request)
    return JsonResponse({'html': html})

@login_required
def chart_data(request):
    if not request.user.accounting_admin:
        return JsonResponse({'success': False, 'message': 'Permission denied'})

    category = request.GET.get('category', '')
    filter_type = request.GET.get('type', '')
    period = request.GET.get('period', 'month')
    
    from datetime import datetime, timedelta
    from django.db.models import Sum, Count
    from django.db.models.functions import TruncDate, TruncMonth, TruncYear
    
    now = timezone.now()
    
    if period == 'month':
        start_date = now.replace(day=1)
        end_date = (start_date + timedelta(days=32)).replace(day=1) - timedelta(days=1)
    elif period == 'quarter':
        quarter_start_month = ((now.month - 1) // 3) * 3 + 1
        start_date = now.replace(month=quarter_start_month, day=1)
        end_date = (start_date + timedelta(days=93)).replace(day=1) - timedelta(days=1)
    else:  # year
        start_date = now.replace(month=1, day=1)
        end_date = now.replace(month=12, day=31)
    
    chart_data = {
        'labels': [],
        'datasets': []
    }
    
    if category == 'loans':
        if filter_type:
            loans = Loan.objects.filter(
                loan_type__loan_type=filter_type,
                created_at__gte=start_date,
                created_at__lte=end_date
            )
        else:
            loans = Loan.objects.filter(
                created_at__gte=start_date,
                created_at__lte=end_date
            )
        
        # Group by month/week/day based on period using Django's date functions
        if period == 'month':
            loans_data = loans.annotate(
                period=TruncDate('created_at')
            ).values('period').annotate(
                total_amount=Sum('amount'),
                count=Count('id')
            ).order_by('period')
        elif period == 'quarter':
            loans_data = loans.annotate(
                period=TruncMonth('created_at')
            ).values('period').annotate(
                total_amount=Sum('amount'),
                count=Count('id')
            ).order_by('period')
        else:  # year
            loans_data = loans.annotate(
                period=TruncYear('created_at')
            ).values('period').annotate(
                total_amount=Sum('amount'),
                count=Count('id')
            ).order_by('period')
        
        # Format labels for display
        if period == 'month':
            labels = [item['period'].strftime('%b %d') for item in loans_data]
        elif period == 'quarter':
            labels = [item['period'].strftime('%b %Y') for item in loans_data]
        else:  # year
            labels = [item['period'].strftime('%Y') for item in loans_data]
        
        chart_data['labels'] = labels
        chart_data['datasets'] = [{
            'label': 'Total Loan Amount',
            'data': [float(item['total_amount'] or 0) for item in loans_data],
            'borderColor': '#2563eb',
            'backgroundColor': 'rgba(37, 99, 235, 0.1)',
            'tension': 0.4
        }]
        
    elif category == 'allowances':
        if filter_type:
            allowances = Allowance.objects.filter(
                allowance_type__allowance_type=filter_type,
                created_at__gte=start_date,
                created_at__lte=end_date
            )
        else:
            allowances = Allowance.objects.filter(
                created_at__gte=start_date,
                created_at__lte=end_date
            )
        
        # Group by month/week/day based on period using Django's date functions
        if period == 'month':
            allowances_data = allowances.annotate(
                period=TruncDate('created_at')
            ).values('period').annotate(
                total_amount=Sum('amount'),
                count=Count('id')
            ).order_by('period')
        elif period == 'quarter':
            allowances_data = allowances.annotate(
                period=TruncMonth('created_at')
            ).values('period').annotate(
                total_amount=Sum('amount'),
                count=Count('id')
            ).order_by('period')
        else:  # year
            allowances_data = allowances.annotate(
                period=TruncYear('created_at')
            ).values('period').annotate(
                total_amount=Sum('amount'),
                count=Count('id')
            ).order_by('period')
        
        # Format labels for display
        if period == 'month':
            labels = [item['period'].strftime('%b %d') for item in allowances_data]
        elif period == 'quarter':
            labels = [item['period'].strftime('%b %Y') for item in allowances_data]
        else:  # year
            labels = [item['period'].strftime('%Y') for item in allowances_data]
        
        chart_data['labels'] = labels
        chart_data['datasets'] = [{
            'label': 'Total Allowance Amount',
            'data': [float(item['total_amount'] or 0) for item in allowances_data],
            'borderColor': '#22c55e',
            'backgroundColor': 'rgba(34, 197, 94, 0.1)',
            'tension': 0.4
        }]
    
    return JsonResponse({
        'success': True,
        'chart_data': chart_data
    })

@login_required
def filter_options(request):
    if not request.user.accounting_admin:
        return JsonResponse({'success': False, 'message': 'Permission denied'})
    
    category = request.GET.get('category', '')
    
    if category == 'loans':
        loan_types = LoanType.objects.all().values_list('loan_type', flat=True)
        return JsonResponse({
            'success': True,
            'options': list(loan_types)
        })
    elif category == 'allowances':
        allowance_types = AllowanceType.objects.all().values_list('allowance_type', flat=True)
        return JsonResponse({
            'success': True,
            'options': list(allowance_types)
        })
    
    return JsonResponse({
        'success': True,
        'options': []
    })

@login_required
def employee_details(request, employee_id):
    if not request.user.accounting_admin:
        return JsonResponse({'success': False, 'message': 'Permission denied'})

    try:
        employee = EmployeeLogin.objects.get(id=employee_id)
        payslips = Payslip.objects.filter(employee=employee).order_by('-cutoff_to')[:5]
        # Inline HTML for payslip list (no employee name or 'Payslips' title)
        payslip_html = ''
        if payslips:
            payslip_html += '<div class="payslips-grid">'
            for payslip in payslips:
                payslip_html += f'''
                <div class="payslip-card">
                    <div class="card-header">
                        <h4>{payslip.cutoff_from.strftime('%b %d')} - {payslip.cutoff_to.strftime('%b %d, %Y')}</h4>
                        <span class="payslip-type-badge {payslip.employee_type}">{payslip.get_employee_type_display()}</span>
                    </div>
                    <div class="card-body">
                        <div class="payslip-details">
                            <div class="detail-row">
                                <span class="label">Period:</span>
                                <span class="value">{payslip.cutoff_from} to {payslip.cutoff_to}</span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Amount:</span>
                                <span class="value highlight">₱{payslip.amount:.2f}</span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Uploaded:</span>
                                <span class="value">{payslip.date_uploaded.strftime('%b %d, %Y')}</span>
                            </div>
                        </div>
                    </div>
                </div>
                '''
            payslip_html += '</div>'
        else:
            payslip_html += ''
        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'name': f"{employee.firstname} {employee.lastname}",
                'idnumber': employee.idnumber,
                'email': employee.email,
            },
            'html': payslip_html
        })
    except EmployeeLogin.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'Employee not found'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})

@login_required
def loans_upload(request):
    if not request.user.accounting_admin:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('dashboard')

    if request.method == 'POST':
        files = request.FILES.getlist('files')
        success_count = 0
        error_count = 0

        for file in files:
            try:
                if file.name.endswith(('.xlsx', '.xls', '.csv')):
                    # Process loan file
                    success, errors = process_loan_file(file, request.user)
                    if success:
                        success_count += 1
                    else:
                        error_count += 1
                        for error in errors:
                            messages.error(request, error)
                else:
                    error_count += 1
                    messages.error(request, f"Invalid file type: {file.name}")
            except Exception as e:
                error_count += 1
                messages.error(request, f"Error processing {file.name}: {str(e)}")

        if success_count > 0:
            messages.success(request, f"Successfully uploaded {success_count} loan file(s)")
        if error_count > 0:
            messages.warning(request, f"Failed to upload {error_count} file(s)")

        return redirect('admin_finance')

    return JsonResponse({'success': False, 'message': 'Invalid request method'})

@login_required
def allowances_upload(request):
    if not request.user.accounting_admin:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('dashboard')

    if request.method == 'POST':
        files = request.FILES.getlist('files')
        success_count = 0
        error_count = 0

        for file in files:
            try:
                if file.name.endswith(('.xlsx', '.xls', '.csv')):
                    # Process allowance file
                    success, errors = process_allowance_file(file, request.user)
                    if success:
                        success_count += 1
                    else:
                        error_count += 1
                        for error in errors:
                            messages.error(request, error)
                else:
                    error_count += 1
                    messages.error(request, f"Invalid file type: {file.name}")
            except Exception as e:
                error_count += 1
                messages.error(request, f"Error processing {file.name}: {str(e)}")

        if success_count > 0:
            messages.success(request, f"Successfully uploaded {success_count} allowance file(s)")
        if error_count > 0:
            messages.warning(request, f"Failed to upload {error_count} file(s)")

        return redirect('admin_finance')

    return JsonResponse({'success': False, 'message': 'Invalid request method'})

def process_loan_file(file, uploaded_by):
    try:
        df = pd.read_excel(file) if file.name.endswith(('.xlsx', '.xls')) else pd.read_csv(file)
        success = True
        errors = []

        for index, row in df.iterrows():
            try:
                idnumber = str(row.get('ID_NO', '')).strip()
                if not idnumber:
                    continue

                try:
                    employee = EmployeeLogin.objects.get(idnumber=idnumber)
                except EmployeeLogin.DoesNotExist:
                    errors.append(f"Employee not found for ID: {idnumber}")
                    continue

                loan_type_name = row.get('Loan_Type', '').strip()
                if not loan_type_name:
                    errors.append(f"Loan type is required for employee {idnumber}")
                    continue

                loan_type, created = LoanType.objects.get_or_create(loan_type=loan_type_name)

                amount = float(row.get('Amount', 0) or 0)
                balance = float(row.get('Balance', 0) or 0)
                monthly_deduction = float(row.get('Monthly_Deduction', 0) or 0)

                Loan.objects.create(
                    employee=employee,
                    loan_type=loan_type,
                    amount=amount,
                    balance=balance,
                    monthly_deduction=monthly_deduction
                )

            except Exception as e:
                errors.append(f"Error processing row {index + 2}: {str(e)}")
                success = False

        return success, errors

    except Exception as e:
        return False, [f"Error reading file: {str(e)}"]

def process_allowance_file(file, uploaded_by):
    try:
        df = pd.read_excel(file) if file.name.endswith(('.xlsx', '.xls')) else pd.read_csv(file)
        success = True
        errors = []

        for index, row in df.iterrows():
            try:
                idnumber = str(row.get('ID_NO', '')).strip()
                if not idnumber:
                    continue

                try:
                    employee = EmployeeLogin.objects.get(idnumber=idnumber)
                except EmployeeLogin.DoesNotExist:
                    errors.append(f"Employee not found for ID: {idnumber}")
                    continue

                allowance_type_name = row.get('Allowance_Type', '').strip()
                if not allowance_type_name:
                    errors.append(f"Allowance type is required for employee {idnumber}")
                    continue

                allowance_type, created = AllowanceType.objects.get_or_create(allowance_type=allowance_type_name)

                amount = float(row.get('Amount', 0) or 0)
                deposit_date = pd.to_datetime(row.get('Deposit_Date', '')).date()

                Allowance.objects.create(
                    employee=employee,
                    allowance_type=allowance_type,
                    amount=amount,
                    deposit_date=deposit_date
                )

            except Exception as e:
                errors.append(f"Error processing row {index + 2}: {str(e)}")
                success = False

        return success, errors

    except Exception as e:
        return False, [f"Error reading file: {str(e)}"]

@login_required
def employee_finance_details(request, employee_id):
    if not request.user.accounting_admin:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('dashboard')

    try:
        employee = EmployeeLogin.objects.get(id=employee_id)
        payslips = Payslip.objects.filter(employee=employee).order_by('-cutoff_to')
        loans = Loan.objects.filter(employee=employee).order_by('-created_at')
        allowances = Allowance.objects.filter(employee=employee).order_by('-created_at')
        
        context = {
            'employee': employee,
            'payslips': payslips,
            'loans': loans,
            'allowances': allowances,
        }
        
        return render(request, 'finance/employee_finance_details.html', context)
        
    except EmployeeLogin.DoesNotExist:
        messages.error(request, "Employee not found.")
        return redirect('admin_finance')
    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")
        return redirect('admin_finance')

def ojt_payslip_template(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="ojt_payslip_template.csv"'
    writer = csv.writer(response)
    writer.writerow([
        'Cut_Off',
        'ID_NO',
        'Regular_Day',
        'ALLOWANCE_DAY',
        'TOTAL_ALLOWANCE',
        'ND_ALLOWANCE',
        'GRAND_TOTAL',
        'BASIC_SCHOOL_SHARE',
        'BASIC_OJT_SHARE',
        'DEDUCTION',
        'NET_OJT_SHARE',
        'RICE_ALLOWANCE',
        'OT_ALLOWANCE',
        'ND_OT_ALLOWANCE',
        'SPECIAL_HOLIDAY',
        'LEGAL_HOLIDAY',
        'SATOFF_ALLOWANCE',
        'RD_OT',
        'ADJUSTMENT',
        'DEDUCTION_2',
        'OT_PAY_ALLOWANCE',
        'TOTAL_ALLOW',
        'LINE',
        'HOLIDAY_DATE',
        'RD_OT_DATE',
        'Perfect_Attendance',
    ])
    return response

@login_required
def download_failed_payslips(request):
    """Generate Excel file with failed payslip uploads"""
    if not request.user.accounting_admin:
        return JsonResponse({'success': False, 'message': 'Permission denied'})

    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)
            errors = data.get('errors', [])

            # Create a new workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Failed Uploads"

            # Add headers
            headers = ['Filename', 'Error']
            for col_num, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col_num, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

            # Add data
            for row_num, error in enumerate(errors, 2):
                ws.cell(row=row_num, column=1, value=error.get('filename', 'Unknown'))
                ws.cell(row=row_num, column=2, value=error.get('error', 'Unknown error'))

            # Adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # Save to BytesIO
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            # Create response
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="payslip_upload_errors.xlsx"'

            return response

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'Error generating file: {str(e)}'}, status=500)

    return JsonResponse({'success': False, 'message': 'Invalid request method'}, status=400)