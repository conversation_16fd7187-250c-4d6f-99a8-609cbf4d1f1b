# Generated by Django 5.2.3 on 2025-07-09 09:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('generalsettings', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='department',
            options={'ordering': ['department_name'], 'verbose_name': 'Department', 'verbose_name_plural': 'Departments'},
        ),
        migrations.AlterModelOptions(
            name='line',
            options={'ordering': ['line_name'], 'verbose_name': 'Production Line', 'verbose_name_plural': 'Production Lines'},
        ),
        migrations.RemoveField(
            model_name='line',
            name='department',
        ),
        migrations.AddField(
            model_name='department',
            name='lines',
            field=models.ManyToManyField(related_name='departments', to='generalsettings.line'),
        ),
        migrations.AlterField(
            model_name='department',
            name='department_name',
            field=models.CharField(max_length=100, unique=True, verbose_name='Department Name'),
        ),
        migrations.AlterField(
            model_name='line',
            name='line_name',
            field=models.CharField(max_length=100, unique=True, verbose_name='Line Name'),
        ),
    ]
