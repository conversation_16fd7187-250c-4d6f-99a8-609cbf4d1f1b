# Generated by Django 5.2.3 on 2025-07-02 06:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PRFRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prf_category', models.CharField(choices=[('government', 'Government Transaction'), ('banking', 'Banking and Finance'), ('hr_payroll', 'Human Resources and Payroll'), ('others', 'Others')], max_length=20)),
                ('prf_type', models.CharField(choices=[('pagibig_loan', 'PAG-IBIG Loan'), ('pagibig_payment_cert', 'PAG-IBIG Certificate of Payment'), ('pagibig_contribution_cert', 'PAG-IBIG Certificate of Contribution'), ('philhealth_form', 'PHILHEALTH Form'), ('sss_loan', 'SSS Loan'), ('sss_maternity', 'SSS Maternity Benefits'), ('sss_sickness', 'SSS Sickness Benefits'), ('bir_form', 'BIR Form (2316/1902)'), ('rcbc_maintenance', 'RCBC Maintenance Form'), ('bank_deposit', 'Bank Deposit'), ('payroll_adjustment', 'Payroll Adjustment'), ('id_replacement', 'ID Replacement'), ('pcoe_compensation', 'PCOE with Compensation'), ('certificate_employment', 'Certificate of Employment'), ('clearance_form', 'Clearance Form'), ('emergency_loan', 'Emergency Loan'), ('medical_loan', 'Medical Assistance Loan'), ('educational_loan', 'Educational Assistance Loan'), ('coop_loan', 'Coop Loan'), ('uniform_ppe', 'Uniform / Caps / PPE / T-shirt'), ('others', 'Others')], max_length=30)),
                ('purpose', models.TextField()),
                ('control_number', models.CharField(blank=True, max_length=50, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('disapproved', 'Disapproved')], default='pending', max_length=15)),
                ('admin_remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_prfs', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prf_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
