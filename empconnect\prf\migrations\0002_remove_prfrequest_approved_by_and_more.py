# Generated by Django 5.2.3 on 2025-07-02 06:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('prf', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='prfrequest',
            name='approved_by',
        ),
        migrations.AddField(
            model_name='prfrequest',
            name='processed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_prfs', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='prfrequest',
            name='prf_category',
            field=models.CharField(choices=[('government', 'Government Transaction'), ('banking', 'Banking and Finance'), ('hr_payroll', 'Human Resources and Payroll')], max_length=20),
        ),
        migrations.AlterField(
            model_name='prfrequest',
            name='prf_type',
            field=models.CharField(choices=[('pagibig_loan', 'PAG-IBIG Loan'), ('pagibig_cert_payment', 'PAG-IBIG Certificate of Payment'), ('pagibig_cert_contribution', 'PAG-IBIG Certificate of Contribution'), ('philhealth_form', 'PHILHEALTH Form'), ('sss_loan', 'SSS Loan'), ('sss_maternity', 'SSS Maternity Benefits'), ('sss_sickness', 'SSS Sickness Benefits'), ('bir_form', 'BIR Form (2316/1902)'), ('rcbc_maintenance', 'RCBC Maintenance Form'), ('bank_deposit', 'Bank Deposit'), ('payroll_adjustment', 'Payroll Adjustment'), ('id_replacement', 'ID Replacement'), ('pcoe_compensation', 'PCOE with Compensation'), ('certificate_employment', 'Certificate of Employment'), ('clearance_form', 'Clearance Form'), ('emergency_loan', 'Emergency Loan'), ('medical_loan', 'Medical Assistance Loan'), ('educational_loan', 'Educational Assistance Loan'), ('coop_loan', 'Coop Loan'), ('uniform_ppe', 'Uniform / Caps / PPE / T-shirt'), ('others', 'Others')], max_length=30),
        ),
    ]
