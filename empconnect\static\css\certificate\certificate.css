.certificate-card {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
    align-items: flex-start;
    flex: 0 0 320px !important;
    max-width: 420px !important;
    min-width: 280px !important;
    width: 320px !important;
    margin: 0;
    display: flex;
    flex-direction: column;
}

.certificates-cell {
    padding: 0;
    background: #fafbfc;
    overflow: visible;
}
.certificates-content {
    max-height: 0;
    overflow: visible;
    transition: max-height 0.25s cubic-bezier(0.4,0,0.2,1);
    padding: var(--space-md);
    display: block;
    align-items: unset;
    justify-content: unset;
}
.certificates-row.open .certificates-content {
    max-height: 1000px;
}

.badge-new {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--error-color);
    color: var(--text-inverse);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    animation: pulseNew 2s infinite;
}

@keyframes pulseNew {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

.certificate-preview {
    position: relative;
    width: 100%;
    height: 200px;
    border-radius: var(--radius-md);
    overflow: hidden;
    background: var(--bg-secondary);
    display: block;
    padding: 0;
    margin-bottom: var(--space-md);
}

.certificate-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: unset;
    border-radius: 0;
    display: block;
    background: transparent;
    max-width: none;
    max-height: none;
    min-width: 0;
    min-height: 200px;
    box-sizing: border-box;
}

.pdf-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    height: 100%;
}

.pdf-icon {
    font-size: 3rem;
    color: var(--error-color);
    margin-bottom: var(--space-sm);
}

.certificate-info {
    margin-bottom: var(--space-md);
}

.certificate-date {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin: 0;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: var(--space-3xl) var(--space-lg);
    min-height: 300px;
}

.empty-state-icon {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: var(--space-lg);
}

.empty-state h3 {
    margin-bottom: var(--space-md);
    color: var(--text-primary);
}

.empty-state p {
    color: var(--text-secondary);
    max-width: 400px;
    margin: 0 auto;
}

.congrats-modal .modal-content {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--text-inverse);
    border: none;
    overflow: visible;
}

.congrats-modal .modal-header,
.congrats-modal .modal-body,
.congrats-modal .modal-footer {
    background: transparent;
    color: var(--text-inverse);
    border-color: rgba(255, 255, 255, 0.2);
}

.congrats-icon {
    color: #ffd700;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.congrats-content {
    text-align: center;
    position: relative;
}

.congrats-animation {
    position: absolute;
    top: -20px;
    left: 0;
    right: 0;
    height: 100px;
    pointer-events: none;
}

/* Enhanced Confetti System */
.confetti-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
    z-index: 1;
}

/* Confetti Cannons */
.confetti-cannon {
    position: absolute;
    bottom: 20px;
    width: 60px;
    height: 60px;
}

.left-cannon {
    left: 30px;
}

.right-cannon {
    right: 30px;
}

/* Confetti Pieces Base Styles */
.confetti-piece, .float-piece, .burst-piece {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

/* Rectangle Confetti */
.confetti-piece.rectangle, .float-piece.rectangle, .burst-piece.rectangle {
    width: 12px;
    height: 8px;
    border-radius: 2px;
}

/* Circle Confetti */
.confetti-piece.circle, .float-piece.circle, .burst-piece.circle {
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

/* Star Confetti */
.confetti-piece.star, .float-piece.star, .burst-piece.star {
    width: 12px;
    height: 12px;
    background: transparent;
    position: relative;
}

.confetti-piece.star::before, .float-piece.star::before, .burst-piece.star::before {
    content: '★';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    line-height: 1;
}

/* Color Variations */
.gold { background: #FFD700; color: #FFD700; }
.pink { background: #FF69B4; color: #FF69B4; }
.blue { background: #00BFFF; color: #00BFFF; }
.green { background: #32CD32; color: #32CD32; }
.orange { background: #FF8C00; color: #FF8C00; }
.purple { background: #9370DB; color: #9370DB; }
.red { background: #FF4444; color: #FF4444; }
.cyan { background: #00FFFF; color: #00FFFF; }
.yellow { background: #FFFF00; color: #FFFF00; }
.lime { background: #00FF00; color: #00FF00; }

/* Cannon Firing Animations */
.confetti-cannon.fire .confetti-piece.launched {
    opacity: 1;
    animation: cannonBlast 3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.left-cannon .confetti-piece:nth-child(1) { animation-delay: 0s; }
.left-cannon .confetti-piece:nth-child(2) { animation-delay: 0.05s; }
.left-cannon .confetti-piece:nth-child(3) { animation-delay: 0.1s; }
.left-cannon .confetti-piece:nth-child(4) { animation-delay: 0.15s; }
.left-cannon .confetti-piece:nth-child(5) { animation-delay: 0.2s; }
.left-cannon .confetti-piece:nth-child(6) { animation-delay: 0.25s; }
.left-cannon .confetti-piece:nth-child(7) { animation-delay: 0.3s; }
.left-cannon .confetti-piece:nth-child(8) { animation-delay: 0.35s; }
.left-cannon .confetti-piece:nth-child(9) { animation-delay: 0.4s; }
.left-cannon .confetti-piece:nth-child(10) { animation-delay: 0.45s; }

.right-cannon .confetti-piece:nth-child(1) { animation-delay: 0s; }
.right-cannon .confetti-piece:nth-child(2) { animation-delay: 0.05s; }
.right-cannon .confetti-piece:nth-child(3) { animation-delay: 0.1s; }
.right-cannon .confetti-piece:nth-child(4) { animation-delay: 0.15s; }
.right-cannon .confetti-piece:nth-child(5) { animation-delay: 0.2s; }
.right-cannon .confetti-piece:nth-child(6) { animation-delay: 0.25s; }
.right-cannon .confetti-piece:nth-child(7) { animation-delay: 0.3s; }
.right-cannon .confetti-piece:nth-child(8) { animation-delay: 0.35s; }
.right-cannon .confetti-piece:nth-child(9) { animation-delay: 0.4s; }
.right-cannon .confetti-piece:nth-child(10) { animation-delay: 0.45s; }

@keyframes cannonBlast {
    0% {
        opacity: 1;
        transform: translateY(0) translateX(0) rotate(0deg) scale(1);
    }
    20% {
        opacity: 1;
        transform: translateY(-100px) translateX(var(--blast-x, 0)) rotate(180deg) scale(1.2);
    }
    50% {
        opacity: 0.8;
        transform: translateY(-200px) translateX(var(--blast-x, 0)) rotate(360deg) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-350px) translateX(var(--blast-x, 0)) rotate(720deg) scale(0.8);
    }
}

/* Different trajectories for left and right cannons */
.left-cannon .confetti-piece:nth-child(1) { --blast-x: 50px; }
.left-cannon .confetti-piece:nth-child(2) { --blast-x: 80px; }
.left-cannon .confetti-piece:nth-child(3) { --blast-x: 120px; }
.left-cannon .confetti-piece:nth-child(4) { --blast-x: 100px; }
.left-cannon .confetti-piece:nth-child(5) { --blast-x: 150px; }
.left-cannon .confetti-piece:nth-child(6) { --blast-x: 70px; }
.left-cannon .confetti-piece:nth-child(7) { --blast-x: 110px; }
.left-cannon .confetti-piece:nth-child(8) { --blast-x: 90px; }
.left-cannon .confetti-piece:nth-child(9) { --blast-x: 130px; }
.left-cannon .confetti-piece:nth-child(10) { --blast-x: 160px; }

.right-cannon .confetti-piece:nth-child(1) { --blast-x: -50px; }
.right-cannon .confetti-piece:nth-child(2) { --blast-x: -80px; }
.right-cannon .confetti-piece:nth-child(3) { --blast-x: -120px; }
.right-cannon .confetti-piece:nth-child(4) { --blast-x: -100px; }
.right-cannon .confetti-piece:nth-child(5) { --blast-x: -150px; }
.right-cannon .confetti-piece:nth-child(6) { --blast-x: -70px; }
.right-cannon .confetti-piece:nth-child(7) { --blast-x: -110px; }
.right-cannon .confetti-piece:nth-child(8) { --blast-x: -90px; }
.right-cannon .confetti-piece:nth-child(9) { --blast-x: -130px; }
.right-cannon .confetti-piece:nth-child(10) { --blast-x: -160px; }

/* Floating Confetti Background */
.floating-confetti {
    position: absolute;
    top: -50px;
    left: 0;
    right: 0;
    height: 120%;
}

.floating-confetti.active .float-piece {
    opacity: 1;
    animation: gentleFloat 8s infinite linear;
}

.floating-confetti .float-piece:nth-child(1) { left: 5%; animation-delay: 0s; }
.floating-confetti .float-piece:nth-child(2) { left: 15%; animation-delay: 0.5s; }
.floating-confetti .float-piece:nth-child(3) { left: 25%; animation-delay: 1s; }
.floating-confetti .float-piece:nth-child(4) { left: 35%; animation-delay: 1.5s; }
.floating-confetti .float-piece:nth-child(5) { left: 45%; animation-delay: 2s; }
.floating-confetti .float-piece:nth-child(6) { left: 55%; animation-delay: 2.5s; }
.floating-confetti .float-piece:nth-child(7) { left: 65%; animation-delay: 3s; }
.floating-confetti .float-piece:nth-child(8) { left: 75%; animation-delay: 3.5s; }
.floating-confetti .float-piece:nth-child(9) { left: 85%; animation-delay: 4s; }
.floating-confetti .float-piece:nth-child(10) { left: 95%; animation-delay: 4.5s; }
.floating-confetti .float-piece:nth-child(11) { left: 10%; animation-delay: 1.2s; }
.floating-confetti .float-piece:nth-child(12) { left: 20%; animation-delay: 2.2s; }
.floating-confetti .float-piece:nth-child(13) { left: 30%; animation-delay: 3.2s; }
.floating-confetti .float-piece:nth-child(14) { left: 40%; animation-delay: 4.2s; }
.floating-confetti .float-piece:nth-child(15) { left: 60%; animation-delay: 0.8s; }

@keyframes gentleFloat {
    0% {
        transform: translateY(-50px) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(500px) rotate(360deg);
        opacity: 0;
    }
}

/* Center Burst Effect */
.confetti-burst {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
}

.center-burst.explode .burst-piece {
    opacity: 1;
    animation: burstExplode 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.center-burst .burst-piece:nth-child(1) { animation-delay: 0s; }
.center-burst .burst-piece:nth-child(2) { animation-delay: 0.1s; }
.center-burst .burst-piece:nth-child(3) { animation-delay: 0.05s; }
.center-burst .burst-piece:nth-child(4) { animation-delay: 0.15s; }
.center-burst .burst-piece:nth-child(5) { animation-delay: 0.08s; }
.center-burst .burst-piece:nth-child(6) { animation-delay: 0.12s; }
.center-burst .burst-piece:nth-child(7) { animation-delay: 0.03s; }
.center-burst .burst-piece:nth-child(8) { animation-delay: 0.18s; }

@keyframes burstExplode {
    0% {
        opacity: 1;
        transform: translate(0, 0) rotate(0deg) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(var(--burst-x, 0), var(--burst-y, 0)) rotate(720deg) scale(0.5);
    }
}

/* Burst directions */
.center-burst .burst-piece:nth-child(1) { --burst-x: 80px; --burst-y: -80px; }
.center-burst .burst-piece:nth-child(2) { --burst-x: -80px; --burst-y: -80px; }
.center-burst .burst-piece:nth-child(3) { --burst-x: 80px; --burst-y: 80px; }
.center-burst .burst-piece:nth-child(4) { --burst-x: -80px; --burst-y: 80px; }
.center-burst .burst-piece:nth-child(5) { --burst-x: 120px; --burst-y: 0px; }
.center-burst .burst-piece:nth-child(6) { --burst-x: -120px; --burst-y: 0px; }
.center-burst .burst-piece:nth-child(7) { --burst-x: 0px; --burst-y: -120px; }
.center-burst .burst-piece:nth-child(8) { --burst-x: 0px; --burst-y: 120px; }

/* Sparkle Effects */
.sparkle-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.sparkle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #FFD700;
    border-radius: 50%;
    opacity: 0;
    box-shadow: 0 0 6px #FFD700;
}

.sparkle:nth-child(1) { top: 20%; left: 15%; }
.sparkle:nth-child(2) { top: 30%; right: 20%; }
.sparkle:nth-child(3) { top: 60%; left: 25%; }
.sparkle:nth-child(4) { top: 70%; right: 15%; }
.sparkle:nth-child(5) { top: 40%; left: 50%; }
.sparkle:nth-child(6) { top: 80%; left: 60%; }
.sparkle:nth-child(7) { top: 25%; left: 70%; }
.sparkle:nth-child(8) { top: 55%; right: 35%; }

.sparkle.twinkle {
    animation: sparkleShine 1.5s ease-in-out infinite;
}

@keyframes sparkleShine {
    0%, 100% {
        opacity: 0;
        transform: scale(0.5);
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
        box-shadow: 0 0 12px #FFD700, 0 0 20px #FFD700;
    }
}



/* Responsive Adjustments */
@media (max-width: 768px) {
    .confetti-cannon {
        width: 40px;
        height: 40px;
    }

    .left-cannon {
        left: 15px;
    }

    .right-cannon {
        right: 15px;
    }

    .confetti-piece, .float-piece, .burst-piece {
        transform: scale(0.8);
    }
}

/* Screen Shake Effect */
@keyframes screenShake {
    0%, 100% { transform: translateX(0); }
    10% { transform: translateX(-2px); }
    20% { transform: translateX(2px); }
    30% { transform: translateX(-2px); }
    40% { transform: translateX(2px); }
    50% { transform: translateX(-1px); }
    60% { transform: translateX(1px); }
    70% { transform: translateX(-1px); }
    80% { transform: translateX(1px); }
    90% { transform: translateX(-1px); }
}



@keyframes confettiFall {
    0% {
        transform: translateY(-100px) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(200px) rotate(360deg);
        opacity: 0;
    }
}

.new-certificates-list {
    margin-top: var(--space-lg);
    text-align: left;
}

.new-certificate-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-sm);
    backdrop-filter: blur(10px);
}

.new-certificate-item i {
    color: #ffd700;
}

.admin-actions-section {
    margin-bottom: var(--space-2xl);
}

.form-help {
    display: block;
    margin-top: var(--space-xs);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.file-label small {
    display: block;
    margin-top: var(--space-xs);
    font-size: var(--font-size-xs);
    opacity: 0.8;
}

.employees-section {
    margin-top: var(--space-2xl);
}

.employees-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.employee-item {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.employee-row{
    padding: 10px 0px;
}

.employee-item:hover {
    box-shadow: var(--shadow-md);
}

.employee-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-lg);
    cursor: pointer;
    transition: background-color var(--transition-normal);
}

.employee-header:hover {
    background: var(--surface-hover);
}

.employee-info {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.employee-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.employee-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-size: 1.5rem;
}

.employee-details h4 {
    margin: 0;
    color: var(--text-primary);
}

.employee-details p {
    margin: 0;
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.expand-icon {
    transition: transform var(--transition-normal);
    color: var(--text-muted);
}

.employee-item.expanded .expand-icon {
    transform: rotate(180deg);
}

.employee-certificates {
    padding: 0 var(--space-lg) var(--space-lg);
    border-top: 1px solid var(--border-color);
    animation: slideDown var(--transition-normal) ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOutDown {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

.certificates-loading {
    text-align: center;
    padding: var(--space-xl);
}

.certificates-grid, .component-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-lg) !important;
    justify-content: flex-start;
    align-items: stretch;
}

.certificate-card {
    flex: 0 0 320px !important;
    max-width: 420px !important;
    min-width: 280px !important;
    width: 320px !important;
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

.certificate-card .card-header h4 {
    font-size: 1.05em;
}

.certificate-card .certificate-image {
    max-width: 100%;
    max-height: 90px;
    border-radius: 6px;
    margin-bottom: 8px;
}

.certificate-card .pdf-preview {
    font-size: 1.5em;
    margin-bottom: 8px;
}

.certificate-card .certificate-info {
    font-size: 0.93em;
    margin-bottom: 6px;
}

.admin-certificate-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    transition: all var(--transition-normal);
}

.admin-certificate-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.admin-certificate-preview {
    width: 100%;
    height: 100%;
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--space-sm);
    background: var(--surface);
    display: flex;
    align-items: center;
    justify-content: center;
}

.admin-certificate-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.admin-certificate-info {
    margin-bottom: var(--space-sm);
}

.admin-certificate-info h5 {
    margin: 0 0 var(--space-xs) 0;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.admin-certificate-info p {
    margin: 0;
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.certificate-status {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: 2px 6px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.certificate-status.seen {
    background: var(--success-color);
    color: white;
}

.certificate-status.unseen {
    background: var(--warning-color);
    color: white;
}

.admin-certificate-actions {
    display: flex;
    gap: var(--space-xs);
    flex-wrap: wrap;
}

.certificate-viewer {
    width: 100%;
    height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    overflow: hidden;
}

#certificateViewModal .certificate-viewer img {
    width: auto;
    height: auto;
    display: block;
    object-fit: contain;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    background: #fff;
}

.certificate-preview-image {
    max-width: 100%;
    max-height: 70vh;
    width: auto;
    height: auto;
    display: block;
    /* margin: auto; */
    object-fit: contain;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    background: #fff;
}

@media (max-width: 768px) {
    .certificate-preview {
        height: 150px;
    }
    
    .certificates-grid {
        grid-template-columns: 1fr;
    }
    
    .employee-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-sm);
    }
    
    .admin-certificate-actions {
        flex-direction: column;
    }
    
    .admin-certificate-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .certificate-preview {
        height: 120px;
    }
    
    .admin-certificate-preview {
        height: 100px;
    }
    
    .employee-header {
        padding: var(--space-md);
    }
    
    .employee-certificates {
        padding: 0 var(--space-md) var(--space-md);
    }
}

.employee-table th, .employee-table td {
    text-align: left;
    vertical-align: middle;
}

.certificate-table-container {
    margin-top: var(--space-md);
    overflow-x: auto;
}

.certificate-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--surface);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.certificate-table th {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    padding: var(--space-md);
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    font-size: var(--font-size-sm);
}

.certificate-table td {
    padding: var(--space-md);
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.certificate-table tbody tr:hover, .certificates-row:hover {
    background: none !important;
    box-shadow: none !important;
}

.certificate-title strong {
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.file-type-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.file-type-badge.image {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.file-type-badge.pdf {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.file-type-badge.other {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.uploaded-by {
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.upload-date {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.status-badge.seen {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.status-badge.unseen {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.certificate-actions {
    display: flex;
    gap: var(--space-xs);
    align-items: center;
}

.certificate-actions .btn {
    padding: 6px 8px;
    min-width: auto;
}

.certificate-actions .btn i {
    font-size: var(--font-size-xs);
}

@media (max-width: 768px) {
    .certificate-table-container {
        margin-top: var(--space-sm);
    }
    
    .certificate-table th,
    .certificate-table td {
        padding: var(--space-sm);
        font-size: var(--font-size-xs);
    }
    
    .certificate-actions {
        flex-direction: column;
        gap: var(--space-xs);
    }
    
    .certificate-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .file-type-badge,
    .status-badge {
        font-size: 10px;
        padding: 2px 6px;
    }
}

.certificate-list.slick-list {
    list-style: none;
    margin: 0;
    padding: 0;
    width: 100%;
}
.certificate-list-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background: var(--surface);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-xs);
    padding: 18px 24px;
    margin-bottom: 14px;
    transition: box-shadow 0.2s;
    gap: 0;
}
.certificate-list-item:last-child {
    margin-bottom: 0;
}
.certificate-list-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.08em;
    flex: 2 1 200px;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 0;
}
.certificate-list-date {
    color: var(--text-muted);
    font-size: 0.98em;
    margin-left: 32px;
    flex: 1 1 120px;
    min-width: 80px;
    margin-bottom: 0;
}
.certificate-list-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.97em;
    font-weight: 500;
    border-radius: 12px;
    padding: 4px 12px;
    margin-left: 32px;
    margin-bottom: 0;
}
.certificate-list-status.seen {
    background: rgba(34, 197, 94, 0.12);
    color: #22c55e;
}
.certificate-list-status.unseen {
    background: rgba(245, 158, 11, 0.12);
    color: #f59e0b;
}
.certificate-list-actions {
    display: flex;
    gap: 10px;
    margin-left: 32px;
    margin-bottom: 0;
}
.certificate-list-empty {
    text-align: center;
    color: var(--text-muted);
    padding: 32px 0;
    font-size: 1.1em;
    font-style: italic;
}
@media (max-width: 700px) {
    .certificate-list-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 16px 12px;
    }
    .certificate-list-title, .certificate-list-date, .certificate-list-status, .certificate-list-actions {
        margin-left: 0;
        margin-bottom: 8px;
    }
    .certificate-list-actions {
        margin-bottom: 0;
    }
}

.certificate-list.card-list {
    list-style: none;
    margin: 0;
    padding: 0;
    width: 100%;
}
.certificate-card-list-item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    background: #f9fbfd;
    border: 1px solid #e5e7eb;
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(30, 41, 59, 0.04);
    padding: 18px 24px;
    margin-bottom: 18px;
    transition: box-shadow 0.2s, border-color 0.2s;
}
.certificate-card-list-item:last-child {
    margin-bottom: 0;
}
.certificate-card-main {
    flex: 1 1 auto;
    min-width: 0;
}

.certificate-card-title {
    font-weight: 600;
    color: #1e293b;
    font-size: 1.08em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 320px;
}
.certificate-card-status {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 0.97em;
    font-weight: 500;
    border-radius: 12px;
    padding: 2px 10px;
}
.certificate-card-status.seen {
    background: rgba(34, 197, 94, 0.12);
    color: #22c55e;
}
.certificate-card-status.unseen {
    background: rgba(245, 158, 11, 0.12);
    color: #f59e0b;
}
.certificate-card-meta {
    color: #64748b;
    font-size: 0.97em;
    margin-bottom: 0;
}
.certificate-card-actions {
    display: flex;
    gap: 10px;
    margin-left: 32px;
    align-items: center;
}
@media (max-width: 700px) {
    .certificate-card-list-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 16px 12px;
    }
    .certificate-card-actions {
        margin-left: 0;
        margin-top: 10px;
    }
    .certificate-card-title {
        max-width: 100%;
    }
}

.certificate-list.card-list-centered {
    list-style: none;
    margin: 0;
    padding: 0;
    width: 100%;
}
.certificate-card-list-item-centered {
    width: 260px;
    min-width: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(30, 41, 59, 0.04);
    padding: 12px 20px;
    margin-bottom: 0;
    transition: box-shadow 0.2s, border-color 0.2s;
    min-height: 56px;
    box-sizing: border-box;
    position: relative;
    overflow: visible;
    z-index: 10;
}
.certificate-card-list-item-centered:last-child {
    margin-bottom: 0;
}
.certificate-card-filetype {
    flex: 0 0 auto;
    margin-right: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.filetype-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 38px;
    border-radius: 12px;
    font-size: 1.5em;
    background: #f3f4f6;
    color: #64748b;
}
.filetype-icon.pdf {
    background: #fef2f2;
    color: #ef4444;
}
.filetype-icon.image {
    background: #f0fdf4;
    color: #22c55e;
}
.filetype-icon.other {
    background: #f3f4f6;
    color: #64748b;
}
.certificate-card-info {
    flex: 1 1 auto;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.certificate-card-title {
    font-weight: 600;
    color: #1e293b;
    font-size: 1.05em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 220px;
}
.certificate-card-date {
    color: #94a3b8;
    font-size: 0.8em;
    margin-top: 2px;
}
.certificate-card-status-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: 18px;
}
.certificate-card-status-icon {
    font-size: 1.1em;
    color: #a3a3a3;
    display: flex;
    align-items: center;
    justify-content: center;
}
.certificate-card-status-icon .fa-check-circle {
    color: #22c55e;
}
.certificate-card-status-icon .fa-exclamation-circle {
    color: #f59e0b;
}
.certificate-card-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}
.certificate-list-empty {
    text-align: center;
    color: var(--text-muted);
    padding: 32px 0;
    font-size: 1.1em;
    font-style: italic;
}
@media (max-width: 600px) {
    .certificate-list.card-list-centered {
        max-width: 100%;
    }
    .certificate-card-list-item-centered {
        flex-direction: column;
        align-items: stretch;
        padding: 12px 10px;
        min-height: 0;
    }
    .certificate-card-filetype {
        margin-right: 0;
        margin-bottom: 8px;
        justify-content: flex-start;
    }
    .certificate-card-info {
        margin-bottom: 8px;
    }
    .certificate-card-status-actions {
        margin-left: 0;
        gap: 8px;
        margin-top: 8px;
    }
}

.certificate-card-actions-dropdown {
    position: relative;
    display: flex;
    align-items: center;
}
.btn.btn-icon.btn-more {
    background: none;
    border: none;
    box-shadow: none;
    padding: 0 8px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
    transition: background 0.15s;
}
.btn.btn-icon.btn-more:hover {
    background: #f3f4f6;
}
.btn.btn-icon.btn-more .dot {
    display: inline-block;
    width: 5px;
    height: 5px;
    background: #64748b;
    border-radius: 50%;
    margin: 0 1.5px;
}
.certificate-dropdown-menu {
    display: none;
    position: absolute;
    top: 36px;
    right: 0;
    min-width: 120px;
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    box-shadow: 0 4px 16px rgba(30,41,59,0.10);
    z-index: 9999;
    padding: 6px 0;
    animation: fadeIn 0.18s;
}
.certificate-dropdown-menu.open {
    display: block;
}
.dropdown-item {
    width: 100%;
    background: none;
    border: none;
    color: #334155;
    font-size: 0.98em;
    padding: 8px 18px;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: background 0.13s;
}
.dropdown-item:hover {
    background: #f3f4f6;
    color: #2563eb;
}
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-6px); }
    to { opacity: 1; transform: translateY(0); }
}

.certificate-list.card-list-flex {
    display: flex;
    flex-wrap: wrap;
    gap: 18px;
    width: 100%;
    align-items: stretch;
}
.certificate-card-list-item-centered {
    width: 260px;
    min-width: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(30, 41, 59, 0.04);
    padding: 12px 20px;
    margin-bottom: 0;
    transition: box-shadow 0.2s, border-color 0.2s;
    min-height: 56px;
    box-sizing: border-box;
    position: relative;
    overflow: visible;
    z-index: 10;
}
.certificate-list-empty {
    flex: 1 1 100%;
    text-align: center;
    color: var(--text-muted);
    padding: 32px 0;
    font-size: 1.1em;
    font-style: italic;
}
@media (max-width: 600px) {
    .certificate-list.card-list-flex {
        gap: 10px;
    }
    .certificate-card-list-item-centered {
        width: 100%;
        min-width: 0;
        padding: 12px 10px;
    }
}

.stats-cards-row {
    display: flex;
    gap: 24px;
    margin: 32px 0 24px 0;
    flex-wrap: wrap;
}
.stats-card {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(30, 41, 59, 0.04);
    padding: 22px 32px 18px 32px;
    min-width: 180px;
    flex: 1 1 180px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
}
.stats-card-label {
    color: #64748b;
    font-size: 1.01em;
    margin-bottom: 8px;
    font-weight: 500;
}
.stats-card-value {
    font-size: 2.1em;
    font-weight: 700;
    color: #2563eb;
    letter-spacing: 1px;
}
@media (max-width: 900px) {
    .stats-cards-row {
        gap: 14px;
    }
    .stats-card {
        padding: 18px 16px 14px 16px;
        min-width: 120px;
    }
}
@media (max-width: 600px) {
    .stats-cards-row {
        flex-direction: column;
        gap: 12px;
    }
    .stats-card {
        width: 100%;
        min-width: 0;
        align-items: stretch;
    }
}

.table-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: var(--radius-lg);
}

[data-theme="dark"] .table-loading-overlay {
    background: rgba(0, 0, 0, 0.9);
}

.table-loading-overlay .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--space-sm);
}

.table-loading-overlay span {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.component-card {
    position: relative;
}

.customTooltip {
    background: var(--surface);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    font-family: 'Poppins', sans-serif;
    font-size: var(--font-size-sm);
    line-height: 1.5;
    max-width: 300px;
    padding: var(--space-lg);
}

.customHighlight {
    background: transparent !important;
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.3) !important;
    border-radius: var(--radius-lg) !important;
    transition: all 0.3s ease !important;
}

[data-theme="dark"] .customTooltip {
    background: var(--surface);
    border-color: var(--border-color);
}

[data-theme="dark"] .customHighlight {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.4) !important;
}

@media (max-width: 991px) {
  .employee-table {
    display: table;
    width: 100%;
  }
  .employee-table thead,
  .employee-table tbody {
    display: table-row-group;
    width: 100%;
  }
  .employee-table tr {
    display: table-row;
    width: 100%;
  }
  .employee-table th,
  .employee-table td {
    display: table-cell;
  }
  .employee-table .col-avatar,
  .employee-table .col-email,
  .employee-table th.col-avatar,
  .employee-table th.col-email,
  .employee-table td.col-avatar,
  .employee-table td.col-email {
    display: none;
  }
}

@media (max-width: 700px) {
  .certificate-card-list-item-centered {
    overflow: visible;
    z-index: 20;
  }
}

.component-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-lg) !important;
    justify-content: flex-start;
    align-items: stretch;
}

/* Upload Progress Overlay */
.upload-progress-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(8px);
}

.upload-progress-content {
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: var(--space-2xl);
    text-align: center;
    box-shadow: var(--shadow-2xl);
    max-width: 400px;
    width: 90%;
    border: 1px solid var(--border-color);
}

.upload-progress-spinner {
    margin-bottom: var(--space-lg);
}

.upload-progress-spinner .spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

.upload-progress-content h3 {
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.upload-progress-content p {
    color: var(--text-secondary);
    margin-bottom: var(--space-lg);
    font-size: var(--font-size-sm);
}

.upload-progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
    margin-top: var(--space-md);
}

.upload-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tablet: 2 cards per row */
@media (max-width: 900px) {
  .component-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
  .certificate-card {
    max-width: 100%;
    min-width: 0;
    width: 100%;
    margin: 0;
  }
}

/* Mobile: 1 card per row */
@media (max-width: 600px) {
  .component-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  .certificate-card {
    max-width: 100%;
    min-width: 0;
    width: 100%;
    margin: 0;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(30,41,59,0.04);
  }
  .certificate-preview {
    height: 140px;
  }
  .card-header, .card-body, .card-footer {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

.card-footer {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    justify-content: end;
    align-items: center;
    flex-wrap: nowrap;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

@media (max-width: 600px) {
  .card-footer .btn {
    flex: 1 1 0;
    min-width: 0;
    font-size: 0.95em;
    padding-left: 0.5em;
    padding-right: 0.5em;
  }
}