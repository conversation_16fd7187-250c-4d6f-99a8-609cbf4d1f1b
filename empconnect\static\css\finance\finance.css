/* Finance Module Specific Styles */

.filter-section {
    margin-bottom: 1rem;
}

.filter-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.filter-type-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-type-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-type-option:hover {
    background: var(--surface-hover);
    border-color: var(--primary-color);
}

.filter-type-option input[type="radio"] {
    accent-color: var(--primary-color);
}

.filter-type-option.selected {
    background: var(--primary-color-light);
    border-color: var(--primary-color);
}

.finance-header {
    margin-bottom: var(--space-xl);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
}

.header-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
    flex: 1;
    max-width: 800px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-xl);
}

.stat-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--space-xs) 0;
}

.stat-info p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

.finance-tabs {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.admin-controls {
    padding: var(--space-xl);
}

.control-section {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    border: 1px solid var(--border-color);
}

.control-section h3 {
    color: var(--text-primary);
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-sm);
    border-bottom: 2px solid var(--primary-color);
}

.upload-prompt {
    pointer-events: none;
}

.upload-prompt i {
    font-size: var(--font-size-3xl);
    color: var(--text-muted);
    margin-bottom: var(--space-md);
}

.upload-prompt p {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: var(--space-sm);
}

.upload-prompt small {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.file-list {
    margin-top: var(--space-md);
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-sm) var(--space-md);
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-sm);
}

.file-info {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.file-icon {
    color: var(--primary-color);
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
}

.file-size {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-left: var(--space-sm);
}

.file-remove {
    background: none;
    border: none;
    color: var(--error-color);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast);
}

.file-remove:hover {
    background: var(--error-color-light);
}

.admin-panel {
    padding: var(--space-xl);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-md);
    border-bottom: 2px solid var(--border-color);
}

.panel-header h3 {
    color: var(--text-primary);
    margin: 0;
}

.search-controls {
    display: flex;
    gap: var(--space-md);
    align-items: center;
}

.search-controls .form-input {
    min-width: 200px;
}

.employees-table-container {
    background: var(--surface);
    overflow: hidden;
}

.employee-section {
    padding: var(--space-xl);
}

.employee-section h3 {
    color: var(--text-primary);
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-sm);
    border-bottom: 2px solid var(--primary-color);
}

.payslips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--space-lg);
}

.payslip-card {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.payslip-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.payslip-card .card-header {
    background: var(--bg-secondary);
    padding: var(--space-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.payslip-card .card-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

.payslip-type-badge {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.payslip-type-badge.regular {
    background: var(--success-color-light);
    color: var(--success-color);
}

.payslip-type-badge.probationary {
    background: var(--warning-color-light);
    color: var(--warning-color);
}

.payslip-type-badge.ojt {
    background: var(--info-color-light);
    color: var(--info-color);
}

.payslip-details {
    padding: var(--space-lg);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-sm);
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-row .label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.detail-row .value {
    color: var(--text-primary);
    font-weight: 600;
}

.detail-row .value.highlight {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.payslip-card .card-footer {
    padding: var(--space-md);
    background: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

.loans-container,
.allowances-container {
    gap: var(--space-xl);
}

.loan-group,
.allowance-group {
    margin-bottom: var(--space-xl);
}

.group-title {
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    padding: var(--space-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.loans-table-container,
.allowances-table-container {
    background: var(--surface);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.status-badge {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: var(--success-color-light);
    color: var(--success-color);
}

.status-badge.paid {
    background: var(--info-color-light);
    color: var(--info-color);
}

.status-badge.suspended,
.status-badge.inactive {
    background: var(--error-color-light);
    color: var(--error-color);
}

.empty-state {
    text-align: center;
    padding: var(--space-3xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--space-lg);
    opacity: 0.5;
}

.empty-state h4 {
    color: var(--text-secondary);
    margin-bottom: var(--space-sm);
}

.empty-state p {
    font-size: var(--font-size-sm);
    max-width: 400px;
    margin: 0 auto;
}

.employee-info {
    margin-bottom: var(--space-lg);
    padding: var(--space-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.employee-info h4 {
    margin: 0 0 var(--space-sm) 0;
    color: var(--text-primary);
}

.employee-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.records-section {
    margin-bottom: var(--space-xl);
}

.records-section h5 {
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    padding: var(--space-sm) var(--space-md);
    background: var(--primary-color-light);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.record-item {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    margin-bottom: var(--space-sm);
    transition: all var(--transition-fast);
}

.record-item:hover {
    background: var(--surface-hover);
    border-color: var(--primary-color);
}

.record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-sm);
}

.record-amount {
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.record-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-sm);
    font-size: var(--font-size-sm);
}

.record-detail {
    display: flex;
    justify-content: space-between;
}

.record-detail .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.record-detail .value {
    color: var(--text-primary);
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading .btn-text {
    opacity: 0;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .header-stats {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
    
    .search-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-controls .form-input {
        min-width: auto;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: stretch;
    }
    
    .header-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--space-sm);
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .payslips-grid {
        grid-template-columns: 1fr;
    }
    
    .panel-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-md);
    }
    
    .record-details {
        grid-template-columns: 1fr;
    }
    
    .control-section {
        padding: var(--space-lg);
    }
    
    .admin-controls,
    .admin-panel,
    .employee-section {
        padding: var(--space-lg);
    }
}

@media (max-width: 480px) {
    .upload-prompt i {
        font-size: var(--font-size-2xl);
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .file-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-sm);
    }
    
    .file-info {
        justify-content: center;
    }
}

/* Loans Table Specific Styles */
.loans-table-container table {
    width: 100%;
    border-collapse: collapse;
}

.loans-table-container th,
.loans-table-container td {
    padding: var(--space-sm) var(--space-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.loans-table-container th {
    background: var(--surface-hover);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.loans-table-container td {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.center-offcanvas {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    text-align: center;
}

.loan-group {
    margin-bottom: var(--space-lg);
}

.loan-group:last-child {
    margin-bottom: 0;
}

/* Import Popover Styles */
.import-popover {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    min-width: 250px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.import-popover.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.employee-info-card {
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    margin-bottom: var(--space-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.employee-header {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.employee-avatar {
    flex-shrink: 0;
}

.employee-avatar .avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
}

.employee-details {
    flex: 1;
}

.employee-details h3 {
    margin: 0 0 var(--space-sm) 0;
    color: var(--text-primary);
    font-size: var(--font-size-2xl);
}

.employee-id {
    margin: 0 0 var(--space-xs) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.employee-email {
    margin: 0 0 var(--space-md) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.employee-department {
    display: flex;
    gap: var(--space-sm);
    flex-wrap: wrap;
}

.department-badge, .line-badge {
    padding: var(--space-xs) var(--space-sm);
    background: var(--primary-color-light);
    color: var(--primary-color);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.line-badge {
    background: var(--accent-color-light);
    color: var(--accent-color);
}

.finance-sections {
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
}

.finance-section {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-lg);
    background: var(--surface-hover);
    border-bottom: 1px solid var(--border-color);
}

.section-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.section-header h3 i {
    color: var(--primary-color);
}

.section-count {
    padding: var(--space-xs) var(--space-sm);
    background: var(--primary-color);
    color: var(--text-inverse);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.section-content {
    padding: var(--space-lg);
}

.empty-state {
    text-align: center;
    padding: var(--space-3xl);
    color: var(--text-muted);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--space-lg);
    opacity: 0.5;
}

.empty-title {
    color: var(--text-secondary);
    margin-bottom: var(--space-sm);
    font-weight: 600;
}

.empty-desc {
    font-size: var(--font-size-sm);
    max-width: 400px;
    margin: 0 auto;
}

.allowances-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.allowance-group {
    background: var(--surface-hover);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    box-shadow: var(--shadow-sm);
}

.allowances-table-container {
    overflow-x: auto;
}

.allowances-table-container table {
    width: 100%;
    border-collapse: collapse;
}

.allowances-table-container th,
.allowances-table-container td {
    padding: var(--space-sm) var(--space-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.allowances-table-container th {
    background: var(--surface-hover);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.allowances-table-container td {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Responsive Design for Employee Finance Details */
@media (max-width: 768px) {
    .page-header-content {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-md);
    }
    
    .page-header-left {
        justify-content: flex-start;
    }
    
    .employee-header {
        flex-direction: column;
        text-align: center;
        gap: var(--space-md);
    }
    
    .employee-department {
        justify-content: center;
    }
    
    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-sm);
    }
    
    .section-header h3 {
        justify-content: center;
    }
    
    .payslips-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .employee-info-card {
        padding: var(--space-lg);
    }
    
    .section-content {
        padding: var(--space-md);
    }
    
    .employee-avatar .avatar {
        width: 60px;
        height: 60px;
    }
    
    .employee-details h3 {
        font-size: var(--font-size-xl);
    }
}

/* Payslip Upload Progress Modal Styles */
.import-status-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-left: auto;
}

.import-status-icon.processing {
    background: var(--primary-light);
    color: var(--primary-color);
}

.import-status-icon.success {
    background: var(--success-bg);
    color: var(--success-color);
}

.import-status-icon.error {
    background: var(--error-bg);
    color: var(--error-color);
}

.progress-section {
    text-align: center;
    padding: 2rem 0;
}

.progress-container {
    margin: 2rem 0;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: var(--surface-hover);
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 6px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.progress-text {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.import-details {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--surface-hover);
    border-radius: var(--radius-md);
    display: flex;
    justify-content: space-around;
    gap: 2rem;
}

.detail-row {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.detail-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 500;
}

.detail-value {
    font-size: 1.5rem;
    font-weight: 700;
}

.detail-value.success {
    color: var(--success-color);
}

.detail-value.error {
    color: var(--error-color);
}

/* Results Modal Styles */
.results-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.summary-card.success {
    background: var(--success-bg);
    border-color: var(--success-color);
}

.summary-card.error {
    background: var(--error-bg);
    border-color: var(--error-color);
}

.summary-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.summary-card.success .summary-icon {
    background: var(--success-color);
    color: white;
}

.summary-card.error .summary-icon {
    background: var(--error-color);
    color: white;
}

.summary-content h4 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

.summary-content p {
    margin: 0.5rem 0 0 0;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 500;
}

.results-tabs {
    margin-top: 2rem;
}

.results-tabs .tab-list {
    display: flex;
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.results-tabs .tab {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    font-weight: 500;
    color: var(--text-muted);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
}

.results-tabs .tab:hover {
    color: var(--primary-color);
}

.results-tabs .tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.results-table-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.results-table {
    width: 100%;
    border-collapse: collapse;
}

.results-table th,
.results-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.results-table th {
    background: var(--surface-hover);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 1;
}

.results-table td {
    color: var(--text-secondary);
}

.results-table tr:hover {
    background: var(--surface-hover);
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.created {
    background: var(--success-bg);
    color: var(--success-color);
}

.status-badge.updated {
    background: var(--warning-bg);
    color: var(--warning-color);
}

.error-message {
    color: var(--error-color);
    font-weight: 500;
}

.error-help {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--surface-hover);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--warning-color);
}

.error-help h5 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-weight: 600;
}

.error-help ul {
    margin: 0;
    padding-left: 1.5rem;
}

.error-help li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

.error-help strong {
    color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .results-summary {
        grid-template-columns: 1fr;
    }

    .import-details {
        flex-direction: column;
        gap: 1rem;
    }

    .results-table-container {
        font-size: var(--font-size-sm);
    }

    .results-table th,
    .results-table td {
        padding: 0.75rem 0.5rem;
    }
}

/* --- Import Type Toggle & Radio Styles (migrated from import-type-radio.css) --- */
.import-type-toggle-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  justify-content: flex-start;
  align-items: flex-start;
}

.import-type-toggle {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.import-type-btn.borderless {
  background: none;
  border: none;
  outline: none;
  box-shadow: none;
  padding: 0.5rem 1.25rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: background 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 160px;
}
.import-type-btn.borderless:hover,
.import-type-btn.borderless.active {
  background: var(--primary-color-light);
  color: var(--primary-color-dark, var(--primary-color));
}

.import-type-radio-group {
  width: 220px;
  background: var(--surface);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  padding: 0.75rem 1rem;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  pointer-events: none;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
}
.import-type-radio-group.active {
  opacity: 1;
  max-height: 200px;
  pointer-events: auto;
  transform: translateY(0);
  margin-top: 0.5rem;
}

.form-radio {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  cursor: pointer;
}
.form-radio:last-child {
  margin-bottom: 0;
}
.form-radio-label {
  margin-left: 0.5rem;
  font-size: 0.95rem;
  color: var(--text-primary);
}