/* Global Styles */
* {
    font-family: 'Poppins', sans-serif !important;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    overflow-x: hidden;
    background-color: #f9f9f9;
    scrollbar-width: thin;
    scrollbar-color: #b0b0b0 #f1f1f1;
}

html {
    overflow-x: hidden;
}

/* For Webkit browsers */
::-webkit-scrollbar {
    width: 7px;
    background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
    background: #b0b0b0;
    border-radius: 6px;
}
::-webkit-scrollbar-thumb:hover {
    background: #888;
}

/* Page entrance animations */
@keyframes ryonan_fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes ryonan_slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes ryonan_slideInLeft {
    from { transform: translateX(-50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes ryonan_slideInRight {
    from { transform: translateX(50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes ryonan_scale {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.ryonan_animated {
    animation-duration: 0.8s;
    animation-fill-mode: both;
}

.ryonan_fadeIn {
    animation-name: ryonan_fadeIn;
}

.ryonan_slideUp {
    animation-name: ryonan_slideUp;
}

.ryonan_slideInLeft {
    animation-name: ryonan_slideInLeft;
}

.ryonan_slideInRight {
    animation-name: ryonan_slideInRight;
}

.ryonan_scale {
    animation-name: ryonan_scale;
}

.ryonan_delay-1 {
    animation-delay: 0.1s;
}

.ryonan_delay-2 {
    animation-delay: 0.3s;
}

.ryonan_delay-3 {
    animation-delay: 0.5s;
}

.ryonan_delay-4 {
    animation-delay: 0.7s;
}

/* Hero Section */
.ryonan_hero {
    height: 100vh;
    position: relative;
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('/static/images/homepage/wallpaper.jpg');
    background-size: cover;
    background-position: center;
}

.ryonan_hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 15% 30%, rgba(74, 123, 255, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 85% 60%, rgba(74, 123, 255, 0.1) 0%, transparent 40%);
    z-index: 0;
}

/* Mouse-responsive particles */
.ryonan_particles_container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.ryonan_particle {
    position: absolute;
    background: rgba(74, 123, 255, 0.3);
    border-radius: 50%;
    pointer-events: none;
    box-shadow: 0 0 8px rgba(74, 123, 255, 0.4);
}

/* Split hero into two columns */
.ryonan_hero_inner {
    display: flex;
    width: 100%;
    max-width: 1500px;
    margin: 0 auto;
    padding: 0;
    position: relative;
    z-index: 5;
}

/* Hero Content */
.ryonan_hero_content {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    padding: 0 5%;
    color: #333;
    max-width: 48%;
    z-index: 10;
}

.ryonan_subtitle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #fff;
    font-weight: 500;
}

.ryonan_hero_title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    line-height: 1.2;
    color: #fff;
}

.ryonan_hero_description {
    margin-bottom: 2rem;
    line-height: 1.6;
    opacity: 0.9;
    font-size: 1.1rem;
    color: #fff;
}

.ryonan_cta_btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    border: solid 1px white;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    width: fit-content;
    min-width: 180px;
    transition: all 0.3s ease;
}

.ryonan_cta_btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Navigation Bar */
.ryonan_navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 20px 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    transition: all 0.3s ease;
}

.ryonan_navbar.scrolled {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 15px 50px;
}

.ryonan_navbar.scrolled .ryonan_logo,
.ryonan_navbar.scrolled .ryonan_nav_link {
    color: #333;
}

.ryonan_navbar.scrolled .ryonan_logo img {
    filter: brightness(0);
}

.ryonan_logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #ffffff;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 12px;
}

.ryonan_logo img {
    height: 32px;
    transition: filter 0.3s ease;
}

.ryonan_nav_links {
    display: flex;
    align-items: center;
    gap: 8px;
}

.ryonan_nav_list {
    display: flex;
    list-style: none;
    margin-right: 30px;
    padding: 0px;
}

.ryonan_nav_item {
    margin-left: 25px;
}

.ryonan_nav_link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    transition: color 0.3s ease;
    position: relative;
}

.ryonan_nav_link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #fff;
    transition: width 0.3s ease;
}

.ryonan_navbar.scrolled .ryonan_nav_link::after {
    background-color: #333;
}

.ryonan_nav_link:hover {
    color: rgba(255, 255, 255, 0.8);
}

.ryonan_navbar.scrolled .ryonan_nav_link:hover {
    color: rgba(0, 0, 0, 0.6);
}

.ryonan_nav_link:hover::after {
    width: 100%;
}

.ryonan_login_btn {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem;
    border-radius: 25px;
    border: solid 1px white;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    width: fit-content;
    min-width: 100px;
    transition: all 0.3s ease;

}

.ryonan_login_btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.ryonan_navbar.scrolled .ryonan_login_btn {
    border: 1px solid #333;
    color: #333;
    background: rgba(255, 255, 255, 0.85);
}

.ryonan_navbar.scrolled .ryonan_login_btn:hover {
    color: #222;
    border-color: #222;
    background: #fff;
}

/* Features Sections */
.ryonan_features_section {
    padding: 80px 0;
    background-color: #f9f9f9;
}

.ryonan_features_section.alternate {
    background-color: #f0f8ff;
}

.ryonan_features_container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.ryonan_section_header {
    text-align: center;
    margin-bottom: 60px;
}

.ryonan_section_header h2 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 15px;
}

.ryonan_section_header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.ryonan_features_grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.ryonan_feature_card {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    transform: translateY(40px);
    opacity: 0;
}

.ryonan_feature_card.animate {
    animation: ryonan_slideUp 0.6s forwards;
}

.ryonan_feature_card:hover {
    transform: translateY(0) scale(1.03);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.ryonan_feature_icon {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ryonan_feature_circle {
    width: 60px;
    height: 60px;
    background-color: #e6f2ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #4a7bff;
    font-style: normal;
}

.ryonan_feature_card h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.ryonan_feature_card p {
    color: #666;
    line-height: 1.6;
}

/* Footer */
.ryonan_footer {
    background-color: #f5f5f5;
    padding: 40px 0;
    text-align: center;
}

.ryonan_footer_content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.ryonan_footer_content p {
    color: #777;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

/* Login Modal */
.ryonan_modal_container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
}

.ryonan_modal_container.active {
    visibility: visible;
    opacity: 1;
}

.ryonan_modal {
    background-color: white;
    width: 90%;
    max-width: 400px;
    border-radius: 10px;
    padding: 60px 30px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(30px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    opacity: 0;
}

.ryonan_modal_container.active .ryonan_modal {
    transform: translateY(0) scale(1);
    opacity: 1;
}

.ryonan_modal_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.ryonan_close_modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #888;
    transition: color 0.3s ease;
}

.ryonan_close_modal:hover {
    color: #333;
}

.ryonan_modal_body form {
    display: flex;
    flex-direction: column;
}

.ryonan_form_group {
    margin-bottom: 20px;
}

.ryonan_form_group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.ryonan_form_group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 15px;
    font-size: 1rem;
    transition: border 0.3s ease;
}

.ryonan_form_group input:focus {
    border-color: #4a7bff;
    outline: none;
}

.ryonan_error_message {
    padding: 10px;
    background-color: #ffebee;
    color: #d32f2f;
    border-radius: 5px;
    margin-bottom: 20px;
    text-align: center;
    display: none;
}

.ryonan_error_message.show {
    display: block;
    animation: ryonan_shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes ryonan_shake {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        transform: translate3d(-3px, 0, 0);
    }
    40%, 60% {
        transform: translate3d(3px, 0, 0);
    }
}

.ryonan_submit_btn {
    background-color: #4a7bff;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ryonan_submit_btn:hover {
    background-color: #3a6ae6;
}

.ryonan_submit_btn .ryonan_spinner {
    display: none;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: ryonan_spin 1s linear infinite;
}

.ryonan_submit_btn.loading .ryonan_spinner {
    display: inline-block;
}

@keyframes ryonan_spin {
    to {
        transform: rotate(360deg);
    }
}

/* Toast Notifications */
.ryonan_toast_container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
}

.ryonan_toast {
    background-color: white;
    color: #333;
    padding: 12px 20px;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.ryonan_toast.show {
    transform: translateX(0);
    opacity: 1;
}

.ryonan_toast.success {
    border-left: 4px solid #4CAF50;
}

.ryonan_toast.error {
    border-left: 4px solid #F44336;
}

.ryonan_toast.info {
    border-left: 4px solid #2196F3;
}

.ryonan_toast.warning {
    border-left: 4px solid #FF9800;
}

/* Password toggle styles */
.ryonan_password_container {
    position: relative;
    display: flex;
    width: 100%;
}

.ryonan_password_container input {
    flex: 1;
    padding-right: 40px;
}

.ryonan_password_toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.3s ease;
}

.ryonan_password_toggle:hover {
    color: #333;
}

.ryonan_password_toggle:focus {
    outline: none;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .ryonan_hero_inner {
        padding: 0 30px;
    }
    
    .ryonan_hero_title {
        font-size: 3rem;
    }
}

@media (max-width: 992px) {
    .ryonan_hero_inner {
        flex-direction: column;
        padding-top: 100px;
    }
    
    .ryonan_hero_content {
        padding-left: 5%;
        padding-right: 5%;
        margin-bottom: 50px;
        max-width: 80%;
        position: relative;
        bottom: 0;
        transform: none;
    }
    
    .ryonan_hero_title {
        font-size: 2.5rem;
    }
    
    .ryonan_hero_description {
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .ryonan_navbar {
        padding: 20px 30px;
    }

    .ryonan_navbar.scrolled {
        padding: 15px 30px;
    }

    .ryonan_logo {
        font-size: 1.6rem;
    }
    
    .ryonan_logo img {
        height: 28px;
    }

    .ryonan_hero_title {
        font-size: 2.2rem;
    }
    
    .ryonan_subtitle {
        font-size: 0.8rem;
    }

    .ryonan_hero_description {
        font-size: 0.95rem;
    }
    
    .ryonan_section_header h2 {
        font-size: 2.2rem;
    }
    
    .ryonan_nav_list {
        display: none;
    }
    
    .ryonan_mobile_menu_btn {
        display: block;
    }
    
    .ryonan_hero_content {
        max-width: 100%;
        padding: 0 20px;
    }
}

@media (max-width: 576px) {
    .ryonan_navbar {
        padding: 15px 20px;
    }

    .ryonan_navbar.scrolled {
        padding: 12px 20px;
    }
    
    .ryonan_logo {
        font-size: 1.4rem;
    }
    
    .ryonan_logo img {
        height: 24px;
    }

    .ryonan_login_btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    .ryonan_hero_title {
        font-size: 1.8rem;
    }
    
    .ryonan_hero_description {
        font-size: 0.9rem;
    }

    .ryonan_modal {
        padding: 20px;
    }
    
    .ryonan_section_header h2 {
        font-size: 1.8rem;
    }
    
    .ryonan_features_grid {
        grid-template-columns: 1fr;
    }
    
    .ryonan_cta_btn {
        width: 100%;
    }
}

/* Mobile Menu */
.ryonan_mobile_menu_btn {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

.ryonan_navbar.scrolled .ryonan_mobile_menu_btn {
    color: #333;
}

.ryonan_mobile_menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    max-width: 300px;
    height: 100vh;
    background: white;
    z-index: 2000;
    padding: 60px 30px;
    transition: right 0.3s ease;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
}

.ryonan_mobile_menu.active {
    right: 0;
}

.ryonan_mobile_menu_close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #333;
    cursor: pointer;
}

.ryonan_mobile_nav_list {
    list-style: none;
    margin-top: 30px;
}

.ryonan_mobile_nav_item {
    margin-bottom: 20px;
}

.ryonan_mobile_nav_link {
    color: #333;
    text-decoration: none;
    font-size: 1.2rem;
    font-weight: 500;
    display: block;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    transition: color 0.3s ease;
}

.ryonan_mobile_nav_link:hover {
    color: #4a7bff;
}

@media (max-width: 768px) {
    .ryonan_mobile_menu_btn {
        display: block;
    }
    
    .ryonan_mobile_menu {
        display: block;
    }
}

.ryonan_customers_section {
    background: #fff;
    padding: 48px 0px;
}
.ryonan_customers_container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}
.ryonan_customers_grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 32px 40px;
    margin-top: 32px;
}
.ryonan_customer_logo {
    height: 40px;
    width: auto;
    object-fit: contain;
    filter: grayscale(0.2) brightness(0.95);
    transition: filter 0.3s, transform 0.3s;
    border-radius: 10px;
    padding: 8px 18px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

@media (max-width: 700px) {
    .ryonan_customers_grid {
        gap: 20px 10px;
    }
    .ryonan_customer_logo {
        height: 40px;
        padding: 4px 8px;
    }
}

.ryonan_carousel_section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    overflow: hidden;
}
.ryonan_carousel {
    position: relative;
    width: 100vw;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.ryonan_carousel_slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 600px;
    background-size: cover;
    background-position: center;
    opacity: 0;
    transition: opacity 0.5s;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}
.ryonan_carousel_slide.active {
    opacity: 1;
    z-index: 2;
}
.ryonan_carousel_text {
    background: rgba(0,0,0,0.45);
    color: #fff;
    padding: 32px 32px 24px 32px;
    border-radius: 12px 12px 0 0;
    margin-bottom: 32px;
    max-width: 700px;
    text-align: center;
    box-shadow: 0 4px 24px rgba(0,0,0,0.12);
}
.ryonan_carousel_text h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 12px;
}
.ryonan_carousel_text p {
    font-size: 1.1rem;
    font-weight: 400;
}
.ryonan_carousel_arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    color: #fff;
    border: none;
    font-size: 2.5rem;
    border-radius: 0;
    width: auto;
    height: auto;
    cursor: pointer;
    z-index: 3;
    transition: color 0.2s;
    box-shadow: none;
    padding: 0 12px;
}
.ryonan_carousel_arrow.left {
    left: 32px;
}
.ryonan_carousel_arrow.right {
    right: 32px;
}
.ryonan_carousel_arrow:hover {
    color: #fff;
    background: none;
}
@media (max-width: 900px) {
    .ryonan_carousel, .ryonan_carousel_slide {
        height: 220px;
    }
    .ryonan_carousel_text {
        padding: 18px 8px 12px 8px;
        font-size: 0.95rem;
        margin-bottom: 12px;
    }
    .ryonan_carousel_text h2 {
        font-size: 1.2rem;
    }
}

.ryonan_register_modal {
    max-width: 600px;
    width: 95%;
}
.ryonan_form_row {
    display: flex;
    gap: 20px;
    margin-bottom: 18px;
}
.ryonan_form_row .ryonan_form_group {
    flex: 1 1 0;
    margin-bottom: 0;
}

.ryonan_modal_title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.2em;
    text-align: center;
    color: #333;
}
.ryonan_modal_subtitle {
    font-size: .8rem;
    color: #888;
    margin-bottom: 1.2em;
    text-align: center;
}

.ryonan_modal_header_centered {
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    gap: 0;
    position: relative;
}
.ryonan_modal_close_topright {
    position: absolute;
    top: 18px;
    right: 18px;
}

.ryonan_modal_switch_prompt {
    text-align: center;
    font-size: .9rem;
    margin-top: 18px;
}
.ryonan_modal_switch_link {
    color: #4a7bff;
    cursor: pointer;
    text-decoration: none;
    font-weight: 500;
}
.ryonan_modal_switch_hr {
    margin: 18px 0;
    border: none;
    border-top: 1px solid #eee;
}