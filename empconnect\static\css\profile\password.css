:root {
    --primary-color: #6366f1;
    --primary-hover: #5856eb;
    --primary-light: #a5b4fc;
    --secondary-color: #64748b;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --surface: #ffffff;
    --surface-hover: #f8fafc;
    
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #a3a3a3;
    --text-inverse: #ffffff;
    
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    
    --shadow-sm: 0 2px 8px 0 rgba(0, 0, 0, 0.066);
    --shadow-md: 0 4px 16px 0 rgba(0, 0, 0, 0.10);
    --shadow-lg: 0 8px 24px 0 rgba(0, 0, 0, 0.13);
    --shadow-xl: 0 16px 32px 0 rgba(0, 0, 0, 0.16);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    --transition-speed: 0.7s;
    
    --sidebar-width-expanded: 250px;
    --sidebar-width-minimized: 70px;
    --header-height: 60px;
    --badge-bg: #ff3860;
}

.password-change-container {
    max-width: 600px;
    margin: 0 auto;
    padding: var(--space-md);
    animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.password-cards-row {
  display: flex;
  justify-content: center;
  align-items: stretch;
  gap: 2rem;
}
.password-card-body {
  padding: var(--space-lg);
  flex: 1 1 0;
  max-width: 30%;
  display: flex;
  flex-direction: column;
}
.reminders-card {
  width: 30%;
  min-width: 260px;
  padding: var(--space-md);
  background: var(--surface);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.password-reminder {
  margin-bottom: var(--space-lg);
  padding: var(--space-lg);
  background: var(--info-bg, #f0f9ff);
  border: 1px solid var(--info-color, #0ea5e9);
  border-radius: var(--radius-md);
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.password-reminder-header i {
  color: var(--info-color, #0ea5e9);
  font-size: 1.2rem;
  margin: var(--space-md) 0px;
  display: flex;
  align-items: flex-start;
  gap: var(--space-sm);
}

.reminders-title {
  margin-bottom: var(--space-sm);
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--info-dark, #0c4a6e);
}

.reminders-subtitle {
  margin: 0;
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  line-height: 1.4;
}

.reminders-list {
  list-style: none;
  padding: 0;
  margin: var(--space-sm) 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.reminders-list li {
  position: relative;
  padding-left: var(--space-lg);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--text-secondary);
}

.reminders-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  top: 0;
  color: var(--primary-color);
  font-weight: bold;
  font-size: 1.2em;
}

.reminders-list li strong {
  color: var(--text-primary);
  font-weight: 600;
}

/* Password Toggle Button Specific Styles */
.password-toggle {
    position: absolute;
    right: var(--space-sm);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: var(--space-sm);
    border-radius: var(--radius-sm);
    cursor: pointer;
    color: var(--text-muted);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    z-index: 10;
}

.password-toggle:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.password-toggle:focus {
    outline: none;
    border: none;
    box-shadow: none;
}

.password-toggle:active {
    outline: none;
    border: none;
    box-shadow: none;
}

/* Override any browser default button styles */
.password-toggle,
.password-toggle:focus,
.password-toggle:active,
.password-toggle:hover {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

/* Prevent red borders on password form inputs */
.password-form .form-input.invalid {
    border-color: var(--border-color) !important;
    box-shadow: none !important;
}

.password-form .form-input:invalid {
    border-color: var(--border-color) !important;
    box-shadow: none !important;
}

.password-toggle i {
    font-size: 14px;
    transition: color var(--transition-fast);
}

/* Ensure input has proper padding for toggle button */
.modern-input-group .form-input {
    padding-right: calc(var(--space-sm) + 32px + var(--space-sm));
}

@media (max-width: 900px) {
  .password-cards-row {
    flex-direction: column;
    align-items: stretch;
  }
  .reminders-card {
    width: 100%;
    margin-top: 2rem;
  }
  .password-card-body {
    width: 100%;
    max-width: 100%;
  }
}

.password-change-card {
    background: var(--surface);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
}

.password-change-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    opacity: 0.8;
}

.password-change-card .card-header {
    padding: var(--space-xl) var(--space-xl) var(--space-lg);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--surface) 100%);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.password-change-card .card-title {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.password-change-card .card-title i {
    color: var(--primary-color);
    font-size: 1.2em;
}

.security-indicator {
    display: flex;
    align-items: center;
}

.security-badge {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.security-badge i {
    font-size: 0.9em;
}

.password-change-card .card-body {
    padding: var(--space-xl);
}

.password-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.form-section {
    position: relative;
}

.form-section .section-title {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    /* margin-bottom: var(--space-lg); */
    /* padding-bottom: var(--space-sm); */
    border-bottom: 2px solid var(--border-color);
    position: relative;
}

.form-section .section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 1px;
}

.password-form .field-error {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    opacity: 0;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.password-form .field-error.show {
    opacity: 1;
    transform: translateY(0);
}

.password-form .field-error::before {
    content: '\f071';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 0.9em;
}

/* Password Tooltip Styles */
.password-tooltip {
    position: absolute;
    top: 60%;
    left: -320px;
    width: 300px;
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--space-lg);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-50%) translateX(-10px);
    transition: all var(--transition-normal);
    z-index: 1000;
    backdrop-filter: blur(8px);
}

.password-tooltip.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(-50%) translateX(0);
}

.password-tooltip::before {
    content: '';
    position: absolute;
    top: 50%;
    right: -8px;
    width: 0;
    height: 0;
    border-left: 8px solid var(--border-color);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    transform: translateY(-50%);
}

.password-tooltip::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -7px;
    width: 0;
    height: 0;
    border-left: 7px solid var(--surface);
    border-top: 7px solid transparent;
    border-bottom: 7px solid transparent;
    transform: translateY(-50%);
}

.tooltip-header {
    margin-bottom: var(--space-md);
}

.tooltip-header h4 {
    margin: 0 0 var(--space-sm) 0;
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--text-primary);
}

.strength-lines {
    display: flex;
    gap: var(--space-xs);
    margin-bottom: var(--space-sm);
}

.strength-lines .line {
    flex: 1;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    transition: all var(--transition-normal);
}

.strength-lines .line.filled.weak {
    background: var(--error-color);
}

.strength-lines .line.filled.fair {
    background: var(--warning-color);
}

.strength-lines .line.filled.good {
    background: #77BEF0;
}

.strength-lines .line.filled.strong {
    background: var(--success-color);
}

.tooltip-content {
    margin-bottom: var(--space-md);
}

.tooltip-content h5 {
    margin: 0 0 var(--space-sm) 0;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.requirements-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.requirements-list li {
    position: relative;
    padding-left: var(--space-sm);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.requirements-list li i {
    color: var(--text-muted);
    font-size: 12px;
    opacity: 0.5;
    transition: all var(--transition-normal);
}

.requirements-list li.met i {
    opacity: 1;
    color: white;
    color: var(--success-color);
}

.requirements-list li.met {
    color: var(--text-muted);
}

.requirements-list li.met span {
    text-decoration: line-through;
}

/* Password Strength Indicator */
.password-strength-indicator {
    margin-top: var(--space-lg);
    padding: var(--space-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.strength-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--space-sm);
}

.strength-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--space-sm);
}

.strength-fill {
    height: 100%;
    border-radius: 4px;
    transition: all var(--transition-slow);
    transform-origin: left;
    transform: scaleX(0);
}

.strength-fill.weak {
    background: var(--error-color);
    transform: scaleX(0.25);
}

.strength-fill.fair {
    background: var(--warning-color);
    transform: scaleX(0.5);
}

.strength-fill.good {
    background: #10b981;
    transform: scaleX(0.75);
}

.strength-fill.strong {
    background: var(--success-color);
    transform: scaleX(1);
}

.strength-text {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-muted);
    text-align: center;
    transition: color var(--transition-normal);
}

.strength-text.weak {
    color: var(--error-color);
}

.strength-text.fair {
    color: var(--warning-color);
}

.strength-text.good {
    color: #10b981;
}

.strength-text.strong {
    color: var(--success-color);
}

/* Form Actions */
.password-form .form-actions {
    display: flex;
    gap: var(--space-md);
    justify-content: flex-end;
    margin-top: var(--space-xl);
    padding-top: var(--space-lg);
    border-top: 1px solid var(--border-color);
}

.password-form .btn {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: var(--font-size-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.password-form .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.password-form .btn:hover::before {
    left: 100%;
}

.password-form .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.password-form .btn:disabled::before {
    display: none;
}

.password-form .btn.loading .btn-text {
    opacity: 0;
}

.password-form .btn.loading .loading-spinner {
    display: flex !important;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

/* Message Container */
.message-container {
    margin-top: var(--space-lg);
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.message {
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-lg);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 500;
    animation: slideInDown 0.3s ease;
    position: relative;
    overflow: hidden;
}

.message::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
}

.message.success {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.message.error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--error-color);
    color: var(--error-color);
}

.message i {
    font-size: 1.1em;
}

.message .message-close {
    margin-left: auto;
    background: none;
    border: none;
    color: currentColor;
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: background var(--transition-fast);
}

.message .message-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

/* Page Header */
.page-subtitle {
    color: var(--text-muted);
    font-size: var(--font-size-md);
    margin: var(--space-xs) 0 0 0;
    font-weight: 400;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Responsive Design */
@media (max-width: 768px) {

    .password-cards-row{
        gap: 0 !important;
    }
    .reminders-subtitle {
        font-size: var(--font-size-sm);
    }

    .reminders-list li {
        font-size: var(--font-size-sm);
    }

    .reminders-card {
        padding: 0;
        margin: 0;
    }

    .password-change-container {
        padding: var(--space-sm);
    }

    .password-change-card .card-header {
        padding: var(--space-lg);
        flex-direction: column;
        gap: var(--space-md);
        text-align: center;
    }

    .password-card-body {
        padding: var(--space-sm);
        width: 100%;
        margin: 0;
    }

    .password-form .form-actions {
        flex-direction: row;
    }

    .password-form .btn {
        /* width: 100%; */
        justify-content: center;
    }

    .password-tooltip {
        position: absolute;
        top: calc(45% + 5px) !important;
        left: 0;
        right: 0;
        width: 60%;
        transform: translateY(0);
        margin: 0;
        z-index:10  }

    .password-tooltip.show {
        transform: translateY(0);
    }

    .password-tooltip::before {
        top: -8px;
        left: 20px;
        right: auto;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid var(--border-color);
        border-top: none;
    }

    .password-tooltip::after {
        top: -7px;
        left: 21px;
        right: auto;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        border-bottom: 7px solid var(--surface);
        border-top: none;
    }

    .tooltip-arrow {
        display: none;
    }
}

@media (max-width: 480px) {
    .password-change-container {
        padding: var(--space-xs);
    }

    .password-change-card .card-header {
        padding: var(--space-md);
    }

    .password-change-card .password-card-body {
        padding: var(--space-md);
        width: 100%;
        max-width: 100%;
    }

    .password-form .form-input {
        padding: var(--space-sm) var(--space-md);
        padding-right: 3rem;
        font-size: var(--font-size-sm);
    }

    .password-form .form-label {
        font-size: var(--font-size-sm);
    }

    .form-section .section-title {
        font-size: var(--font-size-md);
    }

    .security-badge {
        font-size: var(--font-size-xs);
    }
}

/* Dark Theme Adjustments */
[data-theme="dark"] .password-tooltip {
    background: var(--surface);
    border-color: var(--border-color);
}

[data-theme="dark"] .tooltip-arrow {
    background: var(--surface);
    border-color: var(--border-color);
}

[data-theme="dark"] .password-strength-indicator {
    background: var(--bg-tertiary);
}

[data-theme="dark"] .reminders-card {
    background: var(--surface);
    border-color: var(--border-color);
}

[data-theme="dark"] .password-reminder {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
}

[data-theme="dark"] .reminders-title {
    color: var(--text-primary);
}

[data-theme="dark"] .reminders-subtitle {
    color: var(--text-secondary);
}

[data-theme="dark"] .reminders-list li {
    color: var(--text-secondary);
}

[data-theme="dark"] .reminders-list li strong {
    color: var(--text-primary);
}

[data-theme="dark"] .password-card-body {
    background: var(--surface);
    border-color: var(--border-color);
}

[data-theme="dark"] .form-section .section-title {
    color: var(--text-primary);
}

[data-theme="dark"] .form-label {
    color: var(--text-primary);
}

[data-theme="dark"] .form-input {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .form-input:focus {
    border-color: var(--primary-color);
    background: var(--surface);
}

[data-theme="dark"] .form-input::placeholder {
    color: var(--text-muted);
}

[data-theme="dark"] .password-toggle {
    color: var(--text-muted);
}

[data-theme="dark"] .password-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

[data-theme="dark"] .field-error {
    color: var(--error-color);
}

[data-theme="dark"] .tooltip-header h4 {
    color: var(--text-primary);
}

[data-theme="dark"] .tooltip-content h5 {
    color: var(--text-primary);
}

[data-theme="dark"] .requirements-list li {
    color: var(--text-secondary);
}

[data-theme="dark"] .requirements-list li.met {
    color: var(--text-muted);
}

[data-theme="dark"] .requirements-list li.met span {
    color: var(--text-muted);
}

[data-theme="dark"] .form-actions {
    border-color: var(--border-color);
}

[data-theme="dark"] .btn-outline {
    background: transparent;
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .btn-outline:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-hover);
}

[data-theme="dark"] .page-title {
    color: var(--text-primary);
}

[data-theme="dark"] .page-subtitle {
    color: var(--text-secondary);
}

[data-theme="dark"] .component-card {
    background: var(--surface);
    border-color: var(--border-color);
}

/* Focus and Accessibility */
.password-form input:focus,
.password-form button:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.password-form button:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .password-change-card {
        border-width: 2px;
    }
    
    .password-form .form-input {
        border-width: 2px;
    }
    
    .rule-item.valid .rule-icon {
        color: #008000;
    }
}