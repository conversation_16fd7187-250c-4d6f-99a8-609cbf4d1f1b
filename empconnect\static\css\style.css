@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

:root {
    --primary-color: #6366f1;
    --primary-hover: #5856eb;
    --primary-light: #a5b4fc;
    --secondary-color: #64748b;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --surface: #ffffff;
    --surface-hover: #f8fafc;
    
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #a3a3a3;
    --text-inverse: #ffffff;
    
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    
    --shadow-sm: 0 2px 8px 0 rgba(0, 0, 0, 0.066);
    --shadow-md: 0 4px 16px 0 rgba(0, 0, 0, 0.10);
    --shadow-lg: 0 8px 24px 0 rgba(0, 0, 0, 0.13);
    --shadow-xl: 0 16px 32px 0 rgba(0, 0, 0, 0.16);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    --transition-speed: 0.7s;
    
    --sidebar-width-expanded: 250px;
    --sidebar-width-minimized: 70px;
    --header-height: 60px;
    --badge-bg: #ff3860;
}

[data-theme="dark"] {
    --primary-color: #818cf8;
    --primary-hover: #6366f1;
    --primary-light: #4338ca;
    --secondary-color: #94a3b8;
    --accent-color: #22d3ee;
    --success-color: #34d399;
    --warning-color: #fbbf24;
    --error-color: #f87171;
    
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --surface: #1e293b;
    --surface-hover: #334155;
    
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-inverse: #0f172a;
    
    --border-color: #334155;
    --border-hover: #475569;
    
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5), 0 4px 6px -4px rgb(0 0 0 / 0.5);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.6), 0 8px 10px -6px rgb(0 0 0 / 0.6);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f7f7f7;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color var(--transition-normal), color var(--transition-normal);
    position: relative;
    overflow-x: hidden;
}

body.modal-open {
  overflow: hidden;
  position: fixed;
  width: 100vw;
}

ul {
    list-style: none;
}

a {
    text-decoration: none;
    color: inherit;
}

.theme-wave {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    border-radius: 50%;
    transform: scale(0);
    z-index: 9998;
    pointer-events: none;
    opacity: 0;
}

.theme-wave.animate {
    animation: waveExpand 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.theme-wave::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: inherit;
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    animation: waveRipple 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s;
}

.theme-wave::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: inherit;
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    animation: waveRipple 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s;
}

@keyframes waveExpand {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    70% {
        opacity: 0.8;
    }
    100% {
        transform: scale(3);
        opacity: 0;
    }
}

@keyframes waveRipple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0;
    }
}

.container {
    display: flex;
    height: 100vh;
}

.sidebar {
    background-color: var(--surface);
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    transition: width var(--transition-speed) cubic-bezier(0.215, 0.610, 0.355, 1);
    z-index: 100;
    overflow: hidden;
    width: var(--sidebar-width-expanded);
    border-right: 1px solid var(--border-color);
}

.sidebar.minimized {
    width: var(--sidebar-width-minimized);
}

.sidebar-header {
    padding: 5px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
}

.logo-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.logo-info {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
}

.logo-text {
    font-weight: bold;
    font-size: 1.2rem;
    white-space: nowrap;
    opacity: 1;
    transition: opacity var(--transition-speed) ease;
    color: var(--text-primary);
}

.version-text {
    font-size: 0.7rem;
    color: var(--text-muted);
    white-space: nowrap;
    opacity: 1;
    transition: opacity var(--transition-speed) ease;
}

.minimized .logo-text,
.minimized .version-text {
    opacity: 0;
    visibility: hidden;
}

.logo-container:hover {
    transform: scale(1.05);
}

.logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.toggle-btn{
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s, transform var(--transition-speed);
}

.toggle-btn:hover {
    background-color: var(--surface-hover);
}

.minimized .toggle-btn i {
    transform: rotate(180deg);
}

.sidebar-nav {
    overflow-y: auto;
    overflow-x: hidden;
    padding: 15px 0;
    flex: 1 1 auto;
}

.nav-item {
    position: relative;
    transition: background-color 0.2s, border-left 0.3s ease;
    margin-left: 12px;
    margin-right: 12px;
    border-radius: 10px;
    margin-bottom: 15px;
}

.nav-item a {
    padding: 3px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    color: var(--text-secondary);
    transition: color var(--transition-normal);
}

.nav-item:hover {
    background: var(--surface-hover);
    border-radius: 10px;
}

.nav-item.active {
    background: var(--primary-color);
    color: var(--text-inverse);
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border-radius: 10px;
    border-left: none;
    transition: box-shadow 0.2s, background 0.2s, color 0.2s;
}

.nav-item.active svg,
.nav-item.active .nav-text {
    color: var(--text-inverse) !important;
    fill: var(--text-inverse) !important;
    font-weight: 600;
}

.nav-item svg {
    min-width: 40px;
    min-height: 40px;
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px;
    box-sizing: border-box;
}

.nav-text {
    margin-left: 4px;
    font-size: 0.9rem;
    opacity: 1;
    transition: opacity var(--transition-speed) ease;
}

.minimized .nav-text {
    opacity: 0;
    visibility: hidden;
}

.badge {
    background-color: var(--badge-bg);
    color: white;
    border-radius: 10px;
    padding: 2px 8px;
    font-size: 0.7rem;
    margin-left: auto;
    transition: opacity var(--transition-speed) ease, transform 0.3s ease;
}

.minimized .badge {
    position: absolute;
    top: 8px;
    right: 8px;
    opacity: 1;
    transform: scale(0.8);
}

.sidebar-footer {
    position: relative;
    width: 100%;
    padding-top: 10px;
    border-top: 1px solid var(--border-color);
    flex-shrink: 0;
    bottom: 0;
    height: 100px;
}

.user-profile {
    padding: 15px;
    position: relative;
    cursor: pointer;
    transition: background-color 0.2s;
}

.user-profile:hover {
    background-color: var(--surface-hover);
}

.user-info {
    display: flex;
    align-items: center;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.user-profile:hover .avatar {
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(51, 102, 255, 0.4);
}

.user-details {
    margin-left: 10px;
    overflow: hidden;
    transition: opacity var(--transition-speed) ease;
}

.minimized .user-details {
    opacity: 0;
    visibility: hidden;
}

.user-name {
    display: block;
    font-weight: 500;
    white-space: nowrap;
    color: var(--text-primary);
}

.user-department {
    display: block;
    font-size: 0.8rem;
    color: var(--text-muted);
    white-space: nowrap;
}

.dropdown-menu {
    background-color: var(--surface);
    color: var(--text-primary);
    box-shadow: var(--shadow-xl);
    border-radius: 12px;
    padding: 12px;
    opacity: 0;
    visibility: hidden;
    transform-origin: bottom left;
    transform: scale(0.95) translateY(5px);
    transition: opacity 0.2s cubic-bezier(0.215, 0.610, 0.355, 1), transform 0.2s cubic-bezier(0.215, 0.610, 0.355, 1);
    z-index: 9999;
    border: 1px solid var(--border-color);
}

.dropdown-menu.active {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(0);
}

.dropdown-header {
    display: flex;
    align-items: center;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 8px;
}

.dropdown-header .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
}

.dropdown-user-details .user-name {
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
}

.dropdown-user-details .user-email {
    font-size: 0.85rem;
    color: var(--text-muted);
    white-space: nowrap;
}

.dropdown-menu ul {
    padding: 0;
    list-style: none;
}

.dropdown-menu li a {
    padding: 10px 12px;
    display: flex;
    align-items: center;
    color: var(--text-primary);
    border-radius: 8px;
    font-weight: 500;
    transition: background-color 0.2s, color 0.2s;
    white-space: nowrap;
}

.dropdown-menu li a:hover {
    background-color: var(--surface-hover);
    color: var(--primary-color);
}

.dropdown-menu li svg {
    margin-right: 12px;
    fill: currentColor;
    width: 20px;
    height: 20px;
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 8px 0;
}

.dropdown-theme-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    color: var(--text-primary);
    font-weight: 500;
    white-space: nowrap;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

.main-content {
    display: flex;
    flex-direction: column;
    height: 100vh;
    box-shadow: var(--shadow-md);
    background-color: var(--bg-secondary);
    margin-left: var(--sidebar-width-expanded);
    transition: margin-left var(--transition-speed) cubic-bezier(0.215, 0.610, 0.355, 1);
    padding: 0 !important;
    width: 100%;
    position: relative;
    animation: mainContentEntrance 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    opacity: 0;
    transform: translateX(30px);
}

@keyframes mainContentEntrance {
    0% {
        opacity: 0;
        transform: translateX(30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.main-header {
    flex: 0 0 var(--header-height);
    height: var(--header-height);
    position: sticky;
    top: 0;
    z-index: 2;
    background: inherit;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid var(--border-color);
    animation: headerEntrance 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s forwards;
    opacity: 0;
    transform: translateY(-10px);
}

@keyframes headerEntrance {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    padding-right: 20px;
}

.header-action-btn {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-muted);
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 50%;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s, color 0.2s;
}

.header-action-btn:hover {
    background-color: var(--surface-hover);
    color: var(--primary-color);
}

.notification-badge {
    position: absolute;
    top: 4px;
    right: 4px;
    background-color: var(--badge-bg);
    color: white;
    font-size: 0.65rem;
    font-weight: 600;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--surface);
}

.sidebar.expanded ~ .main-content {
    margin-left: var(--sidebar-width-expanded);
}

.page-content {
    flex: 1 1 auto;
    overflow-y: auto;
    min-height: 0;
    width: 100%;
    padding: 15px;
    animation: pageContentEntrance 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s forwards;
    opacity: 0;
    transform: translateY(20px);
    display: flex;
    flex-direction: column;
}

.page-content .card {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.page-content .card .table-container {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-sm);
    margin-top: var(--space-lg);
}

#pageNumbers {
    display: flex;
    flex-direction: row;
    gap: var(--space-xs);
    align-items: center;
}

.pagination-info {
    margin-bottom: 0;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    flex-direction: row;
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 30px;
    height: 30px;
    padding: 0 var(--space-xs);
    /* border: 1px solid var(--border-color); */
    background: var(--surface);
    color: var(--text-secondary);
    border-radius: 50%;
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
}

.pagination-btn:hover {
    background: var(--surface-hover);
    border-color: var(--border-hover);
    color: var(--text-primary);
}

.pagination-btn.active {
    /* background: var(--primary-color);
    border-color: var(--primary-color); */
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-md);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.pagination-btn i {
    font-size: var(--font-size-sm);
}

@keyframes pageContentEntrance {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobile-toggle {
    display: none;
    position: fixed;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 30px;
    height: 35px;
    border-radius: 0 8px 8px 0;
    background-color: var(--primary-color);
    color: white;
    border: none;
    z-index: 200;
    cursor: pointer;
    box-shadow: var(--shadow-md);
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease, background-color 0.3s ease, transform 0.3s ease, left 0.3s ease;
    opacity: 0.5;
}

.mobile-toggle:hover {
    opacity: 1;
    background-color: var(--primary-color);
    animation: pulseButton 1s infinite;
}

.mobile-toggle.active {
    opacity: 1;
    left: calc(var(--sidebar-width-expanded) - 1px);
}

.mobile-toggle i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.mobile-toggle.active i {
    transform: rotate(180deg);
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--space-sm);
    color: var(--text-primary);
}

h1 { 
    font-size: var(--font-size-3xl); 
    font-weight: 700;
}
h2 { 
    font-size: var(--font-size-2xl); 
    font-weight: 600;
}
h3 { 
    font-size: var(--font-size-xl); 
    font-weight: 600;
}
h4 { 
    font-size: var(--font-size-lg); 
    font-weight: 500;
}
h5 { 
    font-size: var(--font-size-md); 
    font-weight: 500;
}
h6 { 
    font-size: var(--font-size-sm); 
    font-weight: 500;
}

p {
    margin-bottom: var(--space-md);
    color: var(--text-secondary);
    font-weight: 400;
}

small {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 400;
}

.nav-tabs {
    display: flex;
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: var(--space-xs);
    margin-bottom: var(--space-xl);
    box-shadow: var(--shadow-md);
    overflow-x: auto;
}

.tab-btn {
    background: transparent;
    border: none;
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    position: relative;
}

.tab-btn:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--primary-color);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.tab-content {
    display: none;
    animation: fadeIn var(--transition-normal);
}

.tab-content.active {
    display: block;
}

.section-title {
    color: var(--text-primary);
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-sm);
    border-bottom: 2px solid var(--border-color);
}

.component-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    border: none;
    font-weight: 400;
    cursor: pointer;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    font-family: inherit;
    gap: 8px;
    outline: none;
    height: 44px;
    padding: 0 24px;
    font-size: 16px;
}

.btn-sm {
    height: 32px;
    padding: 0 16px;
    font-size: 14px;
}

.btn-md {
    height: 44px;
    padding: 0 24px;
    font-size: 16px;
}

.btn-lg {
    height: 52px;
    padding: 0 32px;
    font-size: 16px;
}

.btn.btn-icon {
    padding: 0;
    width: 32px;
    height: 32px;
    justify-content: center;
    font-size: 16px;
    background: transparent;
    color: var(--text-muted);
}

.btn-lg.btn-icon {
    width: calc(var(--space-2xl) * 1.2);
    height: calc(var(--space-2xl) * 1.2);
    font-size: var(--font-size-xl);
}

.btn-sm.btn-icon {
    width: calc(var(--space-2xl) * 0.85);
    height: calc(var(--space-2xl) * 0.85);
    font-size: var(--font-size-md);
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-inverse);
}

.btn-secondary:hover {
    background: var(--text-secondary);
    transform: translateY(-1px);
}

.btn-accent {
    background: var(--accent-color);
    color: var(--text-inverse);
}

.btn-success {
    background: var(--success-color);
    color: var(--text-inverse);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-inverse);
}

.btn-error {
    background: var(--error-color);
    color: var(--text-inverse);
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.btn-outline:hover {
    background: var(--surface-hover);
    border-color: var(--border-hover);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn-loading {
    pointer-events: none;
}

.button-group {
    display: flex;
    gap: var(--space-sm);
    flex-wrap: wrap;
    margin-bottom: var(--space-md);
}

.loading-spinner {
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: 2.5rem 2.5rem 2rem 2.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    /* No box-shadow, no border */
    transform: none !important;
}

.loading-spinner p {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
    transform: none !important;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.dashboard-stats {
    margin-bottom: var(--space-md);
}

.dashboard-stats-container {
    margin-bottom: var(--space-sm);
}

.chart-container {
    height: 100%;
    padding-bottom: var(--space-2xl);
}

#financeChart {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 0 !important;
    flex: 1 1 0 !important;
    padding-bottom: var(--space-2xl);
    box-sizing: border-box;
}

.chart-card-container {
    position: relative;
    margin-bottom: var(--space-md);
    min-height: 500px;
    width: 100% !important;
    max-width: 100% !important;
    min-width: 0 !important;
    flex: 1 1 0 !important;
    background: var(--surface);
    padding: 1.1rem;
    border-radius: 1.1em;
    overflow: visible;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.chart-card-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.chart-card-container:hover::before {
    opacity: 1;
}

.component-card {
    /* position: relative; */
    background: var(--surface);
    padding: var(--space-lg);
    border-radius: 1.1em;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    overflow: visible;
}

.card {
    background: var(--surface);
    padding: 1.2rem 1.1rem 1.1rem 1.1rem;
    border-radius: 1.1em;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    padding: var(--space-sm);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-body {
    padding: var(--space-lg);
}

.card-footer {
    padding: var(--space-md);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: end;
    align-items: center;
    gap: var(--space-sm);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-md);
    color: var(--text-inverse);
    font-size: var(--font-size-xl);
}

.card-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.card-change {
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin: 0;
}

.card-change.positive {
    color: var(--success-color);
}

.card-change.negative {
    color: var(--error-color);
}

.modal {
    opacity: 0;
    visibility: hidden;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
    transition: opacity var(--transition-normal), visibility var(--transition-normal);
}

.modal.show {
    opacity: 1;
    visibility: visible;
    display: flex;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1;
}

.modal-content {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    width: 50%;
    overflow: hidden;
    position: relative;
    z-index: 2;
    transform: scale(0.95);
    transition: transform var(--transition-normal);
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-sm .modal-content {
    max-width: 400px;
}

.modal-md .modal-content {
    max-width: 600px;
}

.modal-lg .modal-content {
    max-width: 800px;
}

.modal-xl .modal-content {
    max-width: 1000px;
}

.modal-header {
    padding: var(--space-md);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    background: var(--surface);
    z-index: 10;
}

.modal-body {
    padding: var(--space-lg);
    overflow-y: auto;
    max-height: calc(90vh - 120px);
}

.modal-footer {
    padding: var(--space-md);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--space-sm);
    justify-content: flex-end;
}

.modal-close {
    background: none;
    border: none;
    padding: var(--space-sm);
    border-radius: var(--radius-sm);
    cursor: pointer;
    color: var(--text-muted);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

/* Ensure modal elements don't interfere when hidden */
.modal:not(.show) {
    pointer-events: none;
}

.modal:not(.show) * {
    pointer-events: none;
}

.modal.show {
    pointer-events: auto;
}

.modal.show * {
    pointer-events: auto;
}

/* Ensure dropdowns in modal have proper z-index */
.modal .dropdown-menu {
    z-index: 10001;
}

.modal select {
    z-index: 10001;
}

/* Import Modal Styles */
.import-actions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.import-description {
    color: var(--text-secondary);
    margin: 0;
    flex: 1;
    margin-right: 1rem;
}

.file-upload-area {
    max-width: 100%;
}

.file-upload {
    position: relative;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--space-md);
    min-height: 120px;
    border: 2px dashed var(--primary-color);
    background: var(--surface-hover);
    border-radius: var(--radius-lg);
    cursor: pointer;
    text-align: center;
    transition: all var(--transition-normal);
}

.file-label:hover {
    border-color: var(--primary-dark);
    background-color: var(--primary-light);
    transform: translateY(-2px);
}

.file-label i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--space-sm);
}

.file-label span {
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: var(--text-primary);
}

.file-label small {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.selected-files {
    margin-top: 1.5rem;
}

.selected-files h4 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.file-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.file-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.file-item i {
    color: var(--success-color);
    font-size: 1.2rem;
}

.file-item span {
    flex: 1;
    font-weight: 500;
}

.file-item small {
    color: var(--text-muted);
}

.upload-progress {
    margin: 1.5rem 0;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-md);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

/* Delete confirmation styles */
.delete-confirmation {
    text-align: center;
    padding: 1rem;
}

.delete-confirmation .warning-icon {
    font-size: 3rem;
    color: var(--warning-color);
    margin-bottom: 1rem;
}

.delete-confirmation p {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.delete-details {
    font-weight: 600;
    color: var(--text-primary);
}

.warning-note {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    padding: 0.75rem;
    background: var(--warning-bg);
    border: 1px solid var(--warning-color);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    color: var(--warning-dark);
}

.warning-note i {
    color: var(--warning-color);
}

/* Import Progress Modal Styles */
.import-progress-container {
    text-align: center;
    padding: var(--space-sm);
}

.import-status-icon {
    margin-bottom: 1.5rem;
}

.import-status-icon i {
    font-size: 4rem;
    color: var(--primary-color);
    animation: pulse 2s infinite;
}

.import-status-icon.success i {
    color: var(--success-color);
    animation: none;
}

.import-status-icon.error i {
    color: var(--error-color);
    animation: none;
}

.import-progress-info {
    margin-bottom: 2rem;
}

.import-progress-info h4 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-size: var(--font-size-xl);
}

.import-progress-info p {
    color: var(--text-secondary);
    margin: 0;
}

.import-progress-bar {
    margin-bottom: 2rem;
}

.progress-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: var(--font-size-sm);
}

.progress-count {
    color: var(--text-muted);
}

.import-warning {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--warning-bg);
    border: 1px solid var(--warning-color);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    color: var(--warning-dark);
    margin-bottom: 1.5rem;
}

.import-warning i {
    color: var(--warning-color);
    font-size: 1.2rem;
}

.import-details {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    /* gap: 2rem; */
    margin-top: 1.5rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.detail-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.detail-value {
    font-size: var(--font-size-xl);
    font-weight: 600;
}

.detail-value.success {
    color: var(--success-color);
}

.detail-value.error {
    color: var(--error-color);
}

/* Import Results Modal Styles */
.import-summary {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.summary-card {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    border: 2px solid;
}

.summary-card.success {
    background: var(--success-bg);
    border-color: var(--success-color);
}

.summary-card.error {
    background: var(--error-bg);
    border-color: var(--error-color);
}

.summary-icon i {
    font-size: 2.5rem;
}

.summary-card.success .summary-icon i {
    color: var(--success-color);
}

.summary-card.error .summary-icon i {
    color: var(--error-color);
}

.summary-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 2rem;
    font-weight: 700;
}

.summary-card.success .summary-content h4 {
    color: var(--success-dark);
}

.summary-card.error .summary-content h4 {
    color: var(--error-dark);
}

.summary-content p {
    margin: 0;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.import-errors h4 {
    margin-bottom: 1rem;
    color: var(--error-color);
}

.error-description {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.error-table-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: 1.5rem;
}

.error-table {
    width: 100%;
    border-collapse: collapse;
}

.error-table th,
.error-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.error-table th {
    background: var(--surface-hover);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 1;
}

.error-table td {
    color: var(--text-secondary);
}

.error-table tr:hover {
    background: var(--surface-hover);
}

.error-help {
    padding: 1rem;
    background: var(--surface-hover);
    border-radius: var(--radius-md);
}

.error-help h5 {
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.error-help ul {
    margin: 0;
    padding-left: 1.5rem;
}

.error-help li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.error-help strong {
    color: var(--text-primary);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.employee-info-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.5em;
    color: var(--text-primary);
}

.employee-info-divider {
    border: none;
    border-top: 2px solid var(--border-color);
    margin-bottom: 1.2em;
}

.employee-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1em 2em;
    margin-bottom: 0.5em;
}

.info-label {
    font-size: 0.98em;
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.1em;
}

.info-value {
    font-size: 1.05em;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5em;
}

.info-details {
    margin-top: 0.5em;
    display: flex;
    gap: 0.5em;
    align-items: flex-start;
}

.tabs-horizontal .tab-list {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 0px;
}

.tab {
    font-size: var(--font-size-md);
    background: none;
    border: none;
    padding: var(--space-md) var(--space-lg);
    cursor: pointer;
    color: var(--text-secondary);
    border-bottom: 2px solid transparent;
    transition: all var(--transition-normal);
    position: relative;
}

.tab:hover {
    color: var(--text-primary);
    background: var(--surface-hover);
}

.tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-panel {
    display: none;
    animation: slideIn var(--transition-normal);
}

.tab-panel.active {
    display: block;
}

.search-box {
    position: relative;
    max-width: 400px;
}

.search-input {
    width: 100%;
    font-size: 0.95rem;
    padding: var(--space-sm) var(--space-sm) var(--space-sm) calc(var(--space-sm) * 4);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--surface);
    color: var(--text-primary);
    transition: all var(--transition-normal);
}

.search-input:focus {
    outline: none;
    border-color: var(--border-color) !important;
    box-shadow: none !important;
}

.search-icon {
    position: absolute;
    left: var(--space-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.search-clear {
    position: absolute;
    right: var(--space-sm);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    cursor: pointer;
    color: var(--text-muted);
    opacity: 0;
    transition: all var(--transition-fast);
}

.search-input:not(:placeholder-shown) ~ .search-clear {
    opacity: 1;
}

.search-clear:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-md) var(--space-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    transition: all var(--transition-normal);
    color: var(--text-primary);
}

.dropdown-toggle:hover {
    border-color: var(--border-hover);
}

.dropdown-toggle i {
    transition: transform var(--transition-normal);
}

.dropdown.open .dropdown-toggle i {
    transform: rotate(180deg);
}

.dropdown .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.dropdown.open .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: all var(--transition-normal);
    cursor: pointer;
    border-radius: 0;
    white-space: nowrap;
}

.dropdown-item:hover {
    background: var(--surface-hover);
    color: var(--primary-color);
}

.dropdown-item:active {
    background: var(--primary-light);
}

.dropdown-item.text-danger {
    color: var(--error-color);
}

.dropdown-item.text-danger:hover {
    background: var(--error-bg);
    color: var(--error-dark);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
    flex-shrink: 0;
}

.dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: 0.5rem 0;
}

/* Action dropdown specific styles */
.action-dropdown {
    position: relative;
    display: inline-block;
    z-index: 1000;
}

.action-dropdown .dropdown-toggle {
    position: relative;
    z-index: 1001;
    background: var(--surface);
    /* border: 1px solid var(--border-color); */
    transition: all var(--transition-normal);
}

.action-dropdown .dropdown-toggle:hover {
    background: var(--surface-hover);
    border-color: var(--primary-color);
    z-index: 1002;
}

.action-dropdown .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 200px;
    z-index: 10000;
    background: var(--surface);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-xl);
    border-radius: var(--radius-md);
    padding: 0.5rem 0;
    margin-top: 2px;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.95) translateY(-10px);
    transition: all 0.2s cubic-bezier(0.215, 0.610, 0.355, 1);
    pointer-events: none;
}

.action-dropdown .dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(0);
    pointer-events: auto;
}

/* Ensure dropdown stays above table rows */
.action-dropdown.open {
    z-index: 10001;
}

.action-dropdown.open .dropdown-toggle {
    z-index: 10002;
    background: var(--surface-hover);
    border-color: var(--primary-color);
}

.action-dropdown.open .dropdown-menu {
    z-index: 10003;
}

/* Ensure table rows don't interfere with dropdowns */
.employee-table tbody tr {
    position: relative;
    z-index: 1;
}

.employee-table tbody tr:hover {
    z-index: 2;
}

.employee-table tbody tr:has(.action-dropdown.open) {
    z-index: 10000;
}

/* Alternative for browsers that don't support :has() */
.employee-table tbody tr.dropdown-open {
    z-index: 10000;
}

.form {
    max-width: 500px;
}

.form-group {
    margin-bottom: var(--space-sm);
}

.form-label {
    display: block;
    margin-bottom: var(--space-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-input {
    width: 100%;
    padding: 10px 14px;
    font-size: 1rem;
    border: 1.5px solid #d1d5db;
    border-radius: 8px;
    background: #fff;
    color: #222;
    outline: none;
    transition: border 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
    margin-top: 4px;
    margin-bottom: 4px;
}
.form-input:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 2px #6366f133;
}
select.form-input {
  appearance: none;
  background: #fff url('data:image/svg+xml;utf8,<svg fill="gray" height="16" viewBox="0 0 20 20" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M7.293 7.293a1 1 0 011.414 0L10 8.586l1.293-1.293a1 1 0 111.414 1.414l-2 2a1 1 0 01-1.414 0l-2-2a1 1 0 010-1.414z"/></svg>') no-repeat right 0.75rem center/1em auto;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input.valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-input.invalid {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-icon {
    position: absolute;
    left: var(--space-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.input-group {
    position: relative;
}

.input-icon {
    position: absolute;
    left: var(--space-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.input-group:has(.input-icon) .form-input {
    padding-left: calc(var(--space-md) * 2.5);
}


.input-action {
    position: absolute;
    right: var(--space-sm);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: var(--space-sm);
    border-radius: var(--radius-sm);
    cursor: pointer;
    color: var(--text-muted);
    transition: all var(--transition-fast);
}

.input-action:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.file-upload {
    position: relative;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-xl);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    color: var(--text-muted);
}

.file-label:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.form-actions {
    display: flex;
    gap: var(--space-sm);
    margin-top: var(--space-xl);
}

.form-actions-left {
    justify-content: flex-start !important;
}

.form-row.form-row-2col {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.form-row.form-row-3col {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.table-container {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--surface);
}

.data-table th,
.data-table td {
    padding: var(--space-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: var(--font-size-sm);
}

.data-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    position: sticky;
    top: 0;
}

.data-table tbody tr {
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.data-table tbody tr:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.data-table tbody tr {
    background-color: var(--surface);
}

.sortable {
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.sortable:hover {
    background: var(--surface-hover);
}

.sortable i {
    margin-left: var(--space-sm);
    opacity: 0.5;
}

.layout-showcase {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--surface);
}

.dashboard-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    min-height: 500px;
}

.layout-showcase .sidebar {
    background: var(--bg-secondary);
    padding: var(--space-lg);
    border-right: 1px solid var(--border-color);
    position: relative;
    height: auto;
    width: auto;
}

.nav-menu {
    margin-top: var(--space-lg);
}

.layout-showcase .main-content {
    padding: var(--space-lg);
    margin-left: 0;
    height: auto;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.2rem;
}

.stat-card {
    background: #fff;
    border-radius: 1.1em;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
    padding: 1.2rem 1.1rem 1.1rem 1.1rem;
    min-height: 120px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    overflow: hidden;
}

.stat-card-icon {
    position: absolute;
    top: 1.1rem;
    right: 1.1rem;
    width: 38px;
    height: 38px;
    background: #ffeaea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
    z-index: 1;
}
.stat-card-icon i {
    color: #ef4444;
    font-size: 1.3em;
}

.stat-card-label {
    font-size: 1.01em;
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.2em;
    z-index: 2;
}

.stat-card-value {
    font-size: 1.6em;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.3em;
    z-index: 2;
}

.stat-card-change {
    font-size: 0.98em;
    font-weight: 600;
    margin-top: 0.1em;
    display: flex;
    align-items: center;
    gap: 0.3em;
    z-index: 2;
}
.stat-card-change.positive { color: #22c55e; }
.stat-card-change.negative { color: #ef4444; }
.stat-card-change i {
    font-size: 1em;
    vertical-align: middle;
}
.stat-card-change-label {
    color: var(--text-muted);
    font-weight: 400;
    margin-left: 0.2em;
}

.stat-content h3 {
    font-size: 1.35rem;
    font-weight: 700;
    margin: 0 0 0.1em 0;
    letter-spacing: 0.5px;
}

.stat-content p {
    color: var(--text-muted);
    font-size: 0.98em;
    font-weight: 500;
    margin: 0;
}

.stat-change {
    font-size: 0.95em;
    font-weight: 600;
    margin-top: 0.2em;
    display: flex;
    align-items: center;
    gap: 0.3em;
}

.stat-change.positive { color: #22c55e; }
.stat-change.negative { color: #ef4444; }

.stat-change i {
    font-size: 1em;
    vertical-align: middle;
}

.toast-container {
    position: fixed;
    bottom: var(--space-lg);
    right: var(--space-lg);
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.toast {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    animation: toastIn 0.3s ease-out;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.persistent-toast {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    position: relative;
    margin-bottom: 8px;
}

.persistent-toast .btn {
    margin-left: 8px;
    white-space: nowrap;
}

.persistent-toast .spinner-border {
    width: 1.2rem;
    height: 1.2rem;
    border-width: 0.15em;
}

.persistent-toast .message-close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    padding: 0;
    font-size: 0.9rem;
    line-height: 1;
}

.persistent-toast .message-close:hover {
    color: rgba(255, 255, 255, 1);
}

.cont {
    display: flex;
    align-items: center;
    transform: scale(1);
    margin-bottom: var(--space-sm);
    cursor: pointer;
}

.cont input[type="checkbox"] {
    height: 1.7rem;
    width: 1.7rem;
    margin: 5px;
    display: inline-block;
    appearance: none;
    position: relative;
    background-color: #F2ECFF;
    border-radius: 15%;
    cursor: pointer;
    overflow: hidden;
}

.cont input[type="checkbox"]::after {
    content: '';
    display: block;
    height: 0.9rem;
    width: .4rem;
    border-bottom: .31rem solid #a0ffe7;
    border-right: .31rem solid #a0ffe7;
    opacity: 0;
    transform: rotate(45deg) translate(-50%, -50%);
    position: absolute;
    top: 45%;
    left: 21%;
    transition: .25s ease;
}

.cont input[type="checkbox"]::before {
    content: '';
    display: block;
    height: 0;
    width: 0;
    background-color: #00C896;
    border-radius: 50%;
    opacity: .5;
    transform: translate(-50%, -50%);
    position: absolute;
    top: 50%;
    left: 50%;
    transition: .3s ease;
}

.cont input[type="checkbox"]:checked::before {
    height: 130%;
    width: 130%;
    opacity: 100%;
}

.cont input[type="checkbox"]:checked::after {
    opacity: 100%;
}

.cont span {
    font-size: 1rem;
    margin-left: var(--space-sm);
    color: var(--text-primary);
}

.cont input[type="checkbox"]:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.cont input[type="checkbox"]:disabled + span {
    opacity: 0.5;
    cursor: not-allowed;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.standard-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: var(--space-xs);
}

.standard-checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    height: 20px;
    width: 20px;
    background-color: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    margin-right: var(--space-sm);
    transition: all var(--transition-normal);
}

.standard-checkbox:hover .checkmark {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.standard-checkbox input:checked ~ .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.standard-checkbox input:checked ~ .checkmark:after {
    display: block;
}

.standard-checkbox span:last-child {
    color: var(--text-primary);
    font-size: var(--font-size-md);
}

.radio-input {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.radio-input * {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.radio-input label {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 0px 20px;
    width: 220px;
    cursor: pointer;
    height: 50px;
    position: relative;
}

.radio-input label::before {
    position: absolute;
    content: "";
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 220px;
    height: 45px;
    z-index: -1;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    border-radius: 10px;
    border: 2px solid transparent;
}

.radio-input label:hover::before {
    transition: all 0.2s ease;
    background-color: var(--bg-secondary);
}

.radio-input .label:has(input:checked)::before {
    background-color: var(--bg-secondary);
    border-color: var(--primary-color);
    height: 50px;
}

.radio-input .label .text {
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 500;
}

.radio-input .label input[type="radio"] {
    background-color: var(--bg-primary);
    appearance: none;
    width: 17px;
    height: 17px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid var(--border-color);
}

.radio-input .label input[type="radio"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    -webkit-animation: pulse 0.7s forwards;
    animation: pulse 0.7s forwards;
}

.radio-input .label input[type="radio"]:before {
    content: "";
    width: 6px;
    height: 6px;
    border-radius: 50%;
    transition: all 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    background-color: #fff;
    transform: scale(0);
}

.radio-input .label input[type="radio"]:checked::before {
    transform: scale(1);
}

.table-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    box-shadow: none;
    margin-bottom: 0.5rem;
    padding: 0;
}

.btn-action {
    display: inline-flex;
    align-items: center;
    gap: 0.4em;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 0.95rem;
    font-weight: 500;
    padding: 0.35em 0.9em;
    border-radius: var(--radius-md);
    transition: background 0.15s, color 0.15s, box-shadow 0.15s;
    cursor: pointer;
}

.btn-action i {
    font-size: 1em;
    margin-right: 0.3em;
}

.btn-action:hover, .btn-action:focus {
    background: var(--surface-hover);
    color: var(--primary-color);
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.filter-show {
    animation: filterSlideIn 0.35s ease-out;
}

.filter-hide {
    animation: filterSlideOut 0.35s ease-out;
}

@keyframes filterSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes filterSlideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-20px);
    }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes toastIn {
    from { transform: translateY(100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes timelineSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
    }
    70% {
        box-shadow: 0 0 0 8px rgba(99, 102, 241, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
    }
}

@keyframes pulseButton {
    0% {
        transform: translateY(-50%) scale(1);
    }
    50% {
        transform: translateY(-50%) scale(1.05);
    }
    100% {
        transform: translateY(-50%) scale(1);
    }
}

@keyframes bounceIcon {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes modalZoomIn {
    from { transform: scale(0.95); opacity: 0; }
    to   { transform: scale(1); opacity: 1; }
}

@keyframes modalZoomOut {
    from { transform: scale(1); opacity: 1; }
    to   { transform: scale(0.95); opacity: 0; }
}

.modal.show .modal-content {
    animation: modalZoomIn 0.2s;
}

.modal.closing .modal-content {
    animation: modalZoomOut 0.2s;
}

/* Timeline Animations */
@keyframes timelineSlideIn {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes timelineDotAppear {
    from {
        opacity: 0;
        transform: scale(0);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes timelineLineGrow {
    from {
        height: 0;
    }
    to {
        height: 100%;
    }
}

/* ===== MODERN STATISTIC CARDS ===== */
.modern-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-md);
}

.modern-stat-card {
    position: relative;
    background: #fff;
    border-radius: 1.1em;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.066);
    padding: 1.2rem 1.1rem 1.1rem 1.1rem;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    overflow: hidden;
}

.modern-stat-icon {
    position: absolute;
    top: 1.1rem;
    right: 1.1rem;
    width: 38px;
    height: 38px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
    z-index: 1;
    background: #f5f5f5;
}

.modern-stat-icon.blue { background: #e6f0ff; color: #2563eb; }
.modern-stat-icon.orange { background: #fff7e6; color: #f59e0b; }
.modern-stat-icon.green { background: #e6f9e6; color: #22c55e; }
.modern-stat-icon.red { background: #ffeaea; color: #ef4444; }

.modern-stat-icon i {
    font-size: 1.3em;
}

.modern-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.modern-stat-card:hover::before {
    opacity: 1;
}

.modern-stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.modern-stat-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.modern-stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    line-height: 1.2;
}

.modern-stat-change {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.modern-stat-change.positive {
    color: #22c55e;
}

.modern-stat-change.negative {
    color: #ef4444;
}

.modern-stat-change-icon {
    font-size: 0.75rem;
    display: flex;
    align-items: center;
}

.modern-stat-change-text {
    color: var(--text-muted);
    font-weight: 400;
    margin-left: 0.25rem;
}

/* Animated trend indicators */
.modern-stat-trend {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 60px;
    height: 30px;
    opacity: 0.1;
    pointer-events: none;
}

.modern-stat-trend.positive {
    background: linear-gradient(45deg, transparent 40%, #22c55e 50%, transparent 60%);
}

.modern-stat-trend.negative {
    background: linear-gradient(-45deg, transparent 40%, #ef4444 50%, transparent 60%);
}

.page-content-container {
    display: flex;
    gap: var(--space-md);
    height: calc(100vh - 6rem);
    padding: var(--space-sm);
    max-width: 100%;
    width: 100%;
    /* background: transparent; */
    align-items: flex-start;
    position: relative;
    z-index: 2;
}

.field-error {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    opacity: 0;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.field-error.show {
    opacity: 1;
    transform: translateY(0);
}

.field-error::before {
    content: '\f071';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 0.9em;
}

@media (max-width: 1024px) {
    :root {
        --sidebar-width-expanded: 220px;
    }
    
    .component-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-layout {
        grid-template-columns: 1fr;
    }
    
    .layout-showcase .sidebar {
        order: 2;
    }
}

@media (max-width: 992px) {
    .container {
        flex-direction: row !important;
    }

    .page-title {
        font-size: 1.3rem;
    }

    .sidebar {
        position: fixed;
        left: -100vw;
        top: 0;
        height: 100vh !important;
        width: var(--sidebar-width-expanded);
        z-index: 1000;
        flex-direction: column;
        justify-content: flex-start;
        transition: left 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        background: var(--surface);
        overflow-x: hidden !important;
    }
    .sidebar.mobile-visible {
        left: 0;
    }
    .main-content {
        width: 100%;
        max-width: 100%;
        margin-left: 0 !important;
        overflow-x: hidden !important;
    }
    .mobile-toggle {
        display: flex;
        position: fixed;
        top: 24px;
        left: 0;
        z-index: 2001;
        opacity: 1;
        background: var(--primary-color);
        color: #fff;
        box-shadow: var(--shadow-md);
        align-items: center;
        justify-content: center;
        transition: left 0.35s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s;
    }
    .sidebar.mobile-visible ~ .mobile-toggle {
        left: calc(var(--sidebar-width-expanded) + 16px);
    }
    .page-content {
        padding: 5px 15px 0 15px;
    }
    .header-actions {
        padding-right: 15px;
    }
    .data-table th,
    .data-table td {
        display: table-cell !important;
    }
    .data-table th:nth-child(1),
    .data-table td:nth-child(1),
    .data-table th:nth-child(5),
    .data-table td:nth-child(5),
    .data-table th:nth-child(7),
    .data-table td:nth-child(7) {
        display: table-cell;
    }
    select,
    select option {
        font-size: 0.95rem;
    }
    .filter-popover .filter-field-select,
    .filter-popover .filter-field-select option {
        font-size: 0.95rem;
    }
    .data-table th, .data-table td {
        font-size: 0.93rem;
    }
    .btn, .btn-sm, .btn-action {
        font-size: 0.93rem;
        padding: 0.35rem 0.85rem;
        min-width: 32px;
        height: 32px;
    }
    .pagination, .pagination-btn, .pagination-info {
        font-size: 0.93rem;
    }
    #pageNumbers, #pageNumbers .pagination-btn, .pagination-btn i {
        font-size: 0.8rem;
    }
    .pagination-btn {
        padding: 0 0.4rem;
        min-width: 24px;
        height: 24px;
    }
    .page-header {
        flex-direction: column;
        align-items: flex-end;
    }
    .page-header-content {
        text-align: right;
        width: 100%;
    }
    .page-actions {
        margin-top: 0.75rem;
        width: 100%;
        display: flex;
        justify-content: flex-end;
    }
    .filter-popover .filter-condition-group label {
        font-size: 0.9em;
    }
    .table-container {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
    }
    .data-table {
        min-width: 600px;
    }
}

@media (max-width: 768px) {

    .page-content-container {
        padding: 0.75rem;
        gap: 1rem;
    }

    .page-content {
        padding: 5px 15px 0 15px;
    }
    #sidebar-toggle{
        display: none;
    }
    .container {
        flex-direction: column !important;
    }
    .sidebar {
        position: fixed;
        left: -100vw;
        top: 0;
        bottom: auto;
        width: var(--sidebar-width-expanded);
        height: 100vh !important;
        flex-direction: column;
        justify-content: flex-start;
        padding: 0;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        transition: left 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        background: var(--surface);
        overflow-x: hidden !important;
    }
    .sidebar.mobile-visible {
        left: 0;
    }
    .main-content {
        width: 100%;
        margin-left: 0 !important;
        margin: 0;
        padding: 0 !important;
        overflow: hidden !important;
    }
    .mobile-toggle {
        display: flex;
        position: fixed;
        top: 24px;
        left: 0;
        z-index: 2001;
        opacity: 1;
        background: var(--primary-color);
        color: #fff;
        box-shadow: var(--shadow-md);
        align-items: center;
        justify-content: center;
        transition: left 0.35s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s;
    }
    .sidebar.mobile-visible ~ .mobile-toggle {
        left: calc(var(--sidebar-width-expanded) + 16px);
    }
    
    .nav-tabs {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--space-sm);
        justify-content: flex-start;
    }
    
    .tab-btn {
        text-align: center;
        width: auto;
        min-width: 120px;
        max-width: 200px;
        flex-shrink: 0;
    }
    
    .button-group {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .button-group .btn {
        width: auto;
        min-width: 120px;
        max-width: 200px;
    }
    
    .card-footer {
        flex-direction: column;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .modal-content {
        width: 95%;
        margin: var(--space-md);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .modern-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .table-container {
        max-width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border-radius: var(--radius-md);
        border: 1px solid var(--border-color);
    }
    
    .data-table {
        min-width: 600px;
        margin: 0;
    }

    .form-row.form-row-2col {
        grid-template-columns: 1fr;
    }
    .form-row.form-row-3col {
        grid-template-columns: 1fr;
    }

    .employee-info-grid {
        grid-template-columns: 1fr;
    }
    select,
    select option {
        font-size: 0.9rem;
    }
    .filter-popover .filter-field-select,
    .filter-popover .filter-field-select option {
        font-size: 0.9rem;
    }
    .data-table th, .data-table td {
        font-size: 0.9rem;
    }
    .btn, .btn-sm, .btn-action {
        font-size: 0.9rem;
        padding: 0.28rem 0.7rem;
        min-width: 28px;
        /* height: 34px; */
    }
    .pagination, .pagination-btn, .pagination-info {
        font-size: 0.9rem;
    }
    #pageNumbers, #pageNumbers .pagination-btn, .pagination-btn i {
        font-size: 0.75rem;
    }
    .page-header {
        flex-direction: column;
        align-items: center;
        gap: 0;
        justify-content: center;
    }
    .page-header-content {
        text-align: left;
        width: 100%;
    }
    .page-actions {
        margin-top: 0.75rem;
        width: 100%;
        display: flex;
        justify-content: flex-end;
    }
    .filter-popover .filter-condition-group label {
        font-size: 0.85em;
    }

    .modern-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 0.875rem;
    }

    .modern-stat-card {
        padding: 1.25rem;
    }

    .modern-stat-value {
        font-size: 1.75rem;
    }

    .chart-container {
        height: 100%;
        padding-bottom: 6rem;
        margin: 0;
    }

    #prfChart{
        height: 100%;
        width: 100%;
    }

    .tab {
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 480px) {
    .page-content-container {
        padding: 0.25rem;
    }

    .header-actions {
        padding-right: 9px;
    }
    .page-content {
        padding: 5px;
        margin: 0 !important;
    }
    .main-content {
        padding: 10px;
    }
    .card {
        padding: 15px;
    }
    .mobile-toggle {
        width: 25px;
    }

    .btn-sm.btn-icon {
        width: calc(var(--space-md) * 0.50);
        height: calc(var(--space-md) * 0.50);
        font-size: var(--font-size-sm);
    }
    
    .table-container {
        font-size: var(--font-size-sm);
        max-width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border-radius: var(--radius-md);
        border: 1px solid var(--border-color);
    }
    
    .data-table {
        min-width: 500px;
        margin: 0;
    }
    
    .data-table th,
    .data-table td {
        padding: var(--space-sm);
        white-space: nowrap;
    }
    
    .component-card {
        padding: var(--space-md);
        min-width: 0;
    }
    
    .nav-tabs {
        gap: var(--space-xs);
    }
    
    .tab-btn {
        width: auto;
        min-width: 100px;
        max-width: 180px;
        font-size: var(--font-size-sm);
        padding: var(--space-sm) var(--space-md);
    }
    
    .button-group .btn {
        width: auto;
        min-width: 100px;
        max-width: 180px;
    }

    .toast-container {
        left: var(--space-md);
        right: var(--space-md);
        bottom: var(--space-md);
    }
    
    .toast {
        min-width: auto;
    }
    select,
    select option {
        font-size: 0.85rem;
    }
    .filter-popover .filter-field-select,
    .filter-popover .filter-field-select option {
        font-size: 0.85rem;
    }
    .data-table th, .data-table td {
        font-size: 0.85rem;
    }
    .btn, .btn-sm, .btn-action {
        font-size: 0.85rem;
        padding: 0.18rem 0.5rem;
        min-width: 24px;
        height: 24px;
    }
    .pagination, .pagination-btn, .pagination-info {
        font-size: 0.85rem;
    }
    #pageNumbers, #pageNumbers .pagination-btn, .pagination-btn i {
        font-size: 0.7rem;
    }
    .filter-popover .filter-condition-group label {
        font-size: 0.8em;
    }

    .modern-stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .modern-stat-card {
        padding: 1rem;
    }

    .modern-stat-value {
        font-size: 1.5rem;
    }

    .modern-stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

*::-webkit-scrollbar {
    width: 6px;
    background: var(--bg-secondary);
}

*::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 6px;
}

*::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

*::-webkit-scrollbar-corner {
    background: var(--bg-secondary);
}

*::-webkit-scrollbar-button {
    display: none;
    width: 0;
    height: 0;
}

@media (prefers-reduced-motion: no-preference) {
    
    .component-card {
        opacity: 1;
        transform: translateY(0);
        transition: all var(--transition-slow);
    }
    
    .component-card.animate {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

*:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --shadow-sm: 0 2px 4px rgba(0,0,0,0.8);
        --shadow-md: 0 4px 8px rgba(0,0,0,0.8);
        --shadow-lg: 0 8px 16px rgba(0,0,0,0.8);
    }
}

.sidebar {
    width: var(--sidebar-width-expanded);
}

.sidebar.minimized {
    width: var(--sidebar-width-minimized);
}
.main-content {
    margin-left: var(--sidebar-width-expanded);
}

.sidebar.minimized ~ .main-content {
    margin-left: var(--sidebar-width-minimized);
}

.sidebar.expanded ~ .main-content {
    margin-left: var(--sidebar-width-expanded);
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

[data-theme="dark"] .loading-overlay {
    background: rgba(15, 23, 42, 0.9);
}

.loading-spinner {
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: 2.5rem 2.5rem 2rem 2.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    /* No box-shadow, no border */
    transform: none !important;
}

.loading-spinner .spinner {
    width: 56px;
    height: 56px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 18px;
}

.loading-spinner p {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
    transform: none !important;
}

.page-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

[data-theme="dark"] .page-loading-overlay {
    background-color: rgba(15, 23, 42, 0.9);
}

.page-loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-xl);
    background-color: var(--surface);
    /* border-radius: var(--radius-lg); */
    /* box-shadow: var(--shadow-xl); */
    /* border: 1px solid var(--border-color); */
}

.page-loading-spinner .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.page-loading-spinner p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.tour-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border-radius: 50%;
    border: none;
    width: 32px;
    height: 32px;
    padding: 0;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    outline: none;
}

.tour-btn:hover,
.tour-btn:focus {
    background: var(--surface-hover);
    box-shadow: var(--shadow-sm);
    transform: scale(1.05);
}
.tour-icon {
    font-size: 1.18em;
    color: var(--text-secondary);
    min-width: 100%;
    min-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1 0 100%;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.tour-btn:hover .tour-icon,
.tour-btn:focus .tour-icon {
    color: var(--primary-color);
    transform: scale(1.1);
}

.tour-tooltip {
    visibility: hidden;
    opacity: 0;
    background: var(--surface);
    color: var(--text-primary);
    text-align: center;
    border-radius: var(--radius-lg);
    padding: var(--space-sm) var(--space-md);
    position: absolute;
    z-index: 9999999;
    top: 50%;
    right: 120%;
    transform: translateY(-50%) translateX(8px);
    font-size: var(--font-size-sm);
    font-weight: 500;
    white-space: nowrap;
    pointer-events: none;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(8px);
}

.tour-tooltip::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -6px;
    transform: translateY(-50%);
    border: 6px solid transparent;
    border-left-color: var(--surface);
    filter: drop-shadow(2px 0 2px rgba(0, 0, 0, 0.1));
}

[data-theme="dark"] .tour-tooltip {
    background: var(--surface);
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .tour-tooltip::after {
    border-left-color: var(--surface);
}
.tour-btn:hover .tour-tooltip,
.tour-btn:focus .tour-tooltip {
    visibility: visible;
    opacity: 1;
    transform: translateY(-50%) translateX(0);
}

.table-actions-left {
    flex: 1;
    display: flex;
    align-items: center;
}

.table-actions-right {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* .search-box {
    max-width: 260px;
    width: 100%;
} */

.filter-card {
    max-width: 100%;
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 1rem;
    margin-top: 0.5rem;
    padding: 1.2rem 1.5rem 1.2rem 1.5rem;
    position: relative;
    overflow: hidden;
    transform: translateY(-30px);
    opacity: 0;
    pointer-events: none;
    transition: transform 0.35s cubic-bezier(0.4,0,0.2,1), opacity 0.35s cubic-bezier(0.4,0,0.2,1);
}

.filter-card.show {
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.filter-close {
    position: absolute;
    top: 0.7rem;
    right: 0.7rem;
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.2rem 0.4rem;
    border-radius: var(--radius-sm);
    transition: background 0.15s;
}

.filter-close:hover {
    background: var(--surface-hover);
    color: var(--error-color);
}

.filter-content {
    margin-top: 0.5rem;
}

.filter-popover {
    position: absolute;
    top: 110%;
    right: 0;
    min-width: 350px;
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 1.2rem 1.2rem 1rem 1.2rem;
    z-index: 9999;
    opacity: 0;
    pointer-events: none;
    transform: translateY(-20px) scale(0.98);
    transition: opacity 0.25s, transform 0.25s;
    max-height: 60vh;
    overflow-y: auto;
    visibility: hidden;
}
.filter-popover.show {
    opacity: 1;
    pointer-events: auto;
    transform: translateY(0) scale(1);
    visibility: visible;
}
.filter-popover-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 55vh;
    overflow-y: auto;
}
.filter-field-select {
    width: 100%;
    padding: 0.5em 0.8em;
    border: 1.5px solid var(--primary-color);
    border-radius: var(--radius-md);
    font-size: 1em;
    outline: none;
    background: var(--surface);
    color: var(--text-primary);
    font-weight: 500;
}
.filter-condition-group {
    display: flex;
    flex-direction: column;
    gap: 0.3em;
    margin-bottom: 0.2em;
}
.filter-condition-group label {
    font-size: 0.98em;
    color: var(--text-secondary);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5em;
    cursor: pointer;
}
.filter-condition-group input[type="radio"] {
    accent-color: var(--primary-color);
    margin-right: 0.4em;
}
.filter-value-input {
    width: 100%;
    padding: 0.5em 0.8em;
    border: 1.5px solid var(--primary-color);
    border-radius: var(--radius-md);
    font-size: 1em;
    outline: none;
    background: var(--surface);
    color: var(--text-primary);
    font-weight: 500;
}
.filter-popover-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.7em;
    margin-top: 0.2em;
}

.table-no-data {
    box-shadow: none !important;
    border: none !important;
    background: none !important;
    padding: 30px !important;
    text-align: center;
}
.table-no-data .empty-icon {
    margin-bottom: 0.5rem;
    font-size: 2.2em;
    color: var(--text-muted);
}
.table-no-data-title {
    font-weight: 600;
    color: var(--text-muted);
    font-size: 1.1em;
}
.table-no-data-desc {
    color: var(--text-secondary);
    font-size: 0.98em;
}

.no-table-border {
    border: none;
    box-shadow: none;
}

.no-table-border .data-table {
    border: none;
}

@media (max-width: 992px) {
    .pagination, .pagination-btn, .pagination-info {
        font-size: 0.85rem;
    }
}

@media (max-width: 768px) {
    .pagination, .pagination-btn, .pagination-info {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .pagination, .pagination-btn, .pagination-info {
        font-size: 0.75rem;
    }
}

@media (max-width: 992px) {
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0;
    }
    .page-header-content {
        text-align: left;
        width: 100%;
    }
    .page-actions {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        margin-top: 0;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0;
    }
    .page-header-content {
        text-align: left;
        width: 100%;
    }
    .page-actions {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        margin-top: 0;
    }
}

.page-header {
    margin: var(--space-md) 0px var(--space-sm) 0px ;
    display: flex;
    justify-content: space-between;
    align-items: start;
}

.status {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    white-space: nowrap;
}

.status-pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-approved {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-disapproved {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.status-cancelled {
    background: rgba(120, 120, 120, 0.1);
    color: #888;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

.details-id {
  font-size: 1.3rem;
  font-weight: 700;
  letter-spacing: 1px;
  margin-right: 0.5rem;
}

.badge {
  display: inline-block;
  padding: 0.18em 0.7em;
  border-radius: 1em;
  font-size: 0.85em;
  font-weight: 600;
  margin-right: 0.3em;
  vertical-align: middle;
}

.badge-green {
  background: #e6f9e6;
  color: #22c55e;
}

.badge-routing {
  background: #fff7e6;
  color: #f59e0b;
}

.details-date {
  color: var(--text-muted);
  font-size: 0.95em;
  font-weight: 500;
  margin-top: 0.2em;
}

.details-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.7rem;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.3rem;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.7rem 2rem;
  margin-bottom: 1.2rem;
}

.details-label {
  color: var(--text-muted);
  font-size: 0.97em;
  font-weight: 500;
}

.details-value {
  color: var(--text-primary);
  font-size: 0.95em;
  font-weight: 500;
  margin-bottom: 0.2em;
}

.details-section.details-details {
  margin-top: 1.2rem;
}

.chart-filters {
    display: flex;
    gap: var(--space-xs);
    background: var(--bg-secondary);
    padding: var(--space-xs);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.chart-filters::before {
    content: '';
    position: absolute;
    top: var(--space-xs);
    bottom: var(--space-xs);
    left: var(--space-xs);
    width: calc(33.333% - var(--space-xs));
    background: var(--primary-color);
    border-radius: var(--radius-sm);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
    opacity: 0;
}

.chart-filters.has-active::before {
    opacity: 1;
}

.chart-filters.slide-0::before {
    transform: translateX(0);
}

.chart-filters.slide-1::before {
    transform: translateX(calc(100% + var(--space-xs)));
}

.chart-filters.slide-2::before {
    transform: translateX(calc(200% + 2 * var(--space-xs)));
}

.chart-filters .filter-btn.active {
    color: var(--text-inverse);
    background: var(--primary-color);
}

.filter-btn {
    background: transparent;
    border: none;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    z-index: 2;
    flex: 1;
    text-align: center;
    white-space: nowrap;
}

.filter-btn:hover {
    background: rgba(var(--primary-color-rgb), 0.1);
    color: var(--text-primary);
}

.filter-btn.active {
    color: var(--text-inverse);
    background: transparent;
}

.chart-type-filters {
    display: flex;
    gap: var(--space-xs);
    background: var(--bg-secondary);
    padding: var(--space-xs);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.chart-type-filters::before {
    content: '';
    position: absolute;
    top: var(--space-xs);
    bottom: var(--space-xs);
    left: var(--space-xs);
    width: calc(50% - var(--space-xs));
    background: var(--primary-color);
    border-radius: var(--radius-sm);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
    opacity: 0;
}

.chart-type-filters.has-active::before {
    opacity: 1;
}

.chart-type-filters.slide-0::before {
    transform: translateX(0);
}

.chart-type-filters.slide-1::before {
    transform: translateX(calc(100% + var(--space-xs)));
}

.chart-type-btn {
    background: transparent;
    border: none;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    z-index: 2;
    flex: 1;
    text-align: center;
    white-space: nowrap;
}

.chart-type-btn.active {
    color: var(--text-inverse);
    background: transparent;
}

@media (max-width: 768px) {
    .chart-controls {
        width: 100%;
        display: flex;
        gap: var(--space-xs);
        align-items: center;
        flex-wrap: nowrap;
    }

    .chart-header {
        flex-direction: column;
        width: 100%;
    }
    .chart-header h3 {
        width: 100%;
        margin-bottom: 0.2rem;
        text-align: left;
    }

    .chart-filters {
        display: flex;
        gap: var(--space-xs);
        background: var(--bg-secondary);
        padding: var(--space-xs);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-color);
        position: relative;
        overflow: hidden;
        flex-grow: 1;
        min-width: 0;
    }

    .chart-filters .filter-btn {
        padding: var(--space-xs) var(--space-sm);
        font-size: var(--font-size-sm);
        min-width: 0;
        flex: 1;
    }

    .chart-type-filters {
        white-space: nowrap;
        flex-grow: 1;
    }

    .chart-type-filters .chart-type-btn {
        padding: var(--space-xs) var(--space-sm);
        font-size: var(--font-size-sm);
    }
}

.chart-type-filters .chart-type-btn.active {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1.2rem;
    overflow: visible;
}

@media (max-width: 480px) {
    .chart-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.7rem;
    }
    .chart-header h3 {
        margin-bottom: 0.2rem;
        text-align: left;
    }
    .chart-controls {
        width: 100%;
        justify-content: flex-start;
        gap: var(--space-xs);
    }

    .chart-filters .filter-btn {
        padding: var(--space-xs);
        font-size: var(--font-size-xs);
        min-width: 0;
    }

    .chart-type-filters .chart-type-btn {
        padding: var(--space-xs);
        font-size: var(--font-size-xs);
    }
}

.chart-controls {
    display: flex;
    gap: var(--space-md);
    align-items: center;
    position: relative;
    overflow: visible;
}

.data-table td, .data-table th {
    vertical-align: middle;
    text-align: center;
}
.data-table td .standard-checkbox,
.data-table th .standard-checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

.data-table .standard-checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}
.data-table .standard-checkbox .checkmark {
    height: 20px;
    width: 20px;
    background-color: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    margin-right: var(--space-sm);
    transition: all var(--transition-normal);
}

[data-theme="dark"] .modern-stat-card,
[data-theme="dark"] .component-card,
[data-theme="dark"] .chart-card-container,
[data-theme="dark"] .statistic-card,
[data-theme="dark"] .file-upload .file-label,
[data-theme="dark"] .form-input,
[data-theme="dark"] .select,
[data-theme="dark"] .card {
  border: 1.5px solid var(--border-color) !important;
  background: var(--bg-secondary) !important;
}

[data-theme="dark"] .form-input,
[data-theme="dark"] .select {
  color: white;
  font-weight: 200 !important;
}

[data-theme="dark"] .modern-stat-card,
[data-theme="dark"] .tour-btn {
  color: #e0e6ed !important;
  background: var(--bg-secondary) !important;
}
[data-theme="dark"] .modern-stat-label,
[data-theme="dark"] .modern-stat-value,
[data-theme="dark"] .modern-stat-change {
  color: #e0e6ed !important;
}

.admin-prf-table th,
.admin-prf-table td {
    display: table-cell !important;
}

.file-upload-area {
    max-width: 600px;
    margin: 0 auto;
}

.file-upload .file-label {
    min-height: 120px;
    border: 2px dashed var(--primary-color);
    background: #f3f6fd;
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
    padding: var(--space-xl);
    border-radius: var(--radius-lg);
    cursor: pointer;
    text-align: center;
}

.file-upload .file-label:hover {
    border-color: var(--primary-color);
    background-color: var(--surface-hover);
}

.file-upload .file-label.drag-over {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
    transform: scale(1.02);
}

.file-upload .file-label small {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.file-upload .file-label span {
    font-size: var(--font-size-s);
    color: var(--text-secondary);
}

.file-upload .file-label i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--space-sm);
}

/* Intro.js Custom Styles (moved from certificate.css) */
.introjs-tooltip {
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow-lg) !important;
    border: 1px solid var(--border-color) !important;
}

.introjs-tooltip-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--font-size-md);
    margin-bottom: var(--space-sm);
}

.introjs-tooltip-header{
    padding: 10px;
}

.introjs-tooltiptext {
    color: var(--text-secondary);
    margin-bottom: var(--space-md);
}

.introjs-button {
    background: var(--primary-color);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-sm) var(--space-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    margin: 0 var(--space-xs);
}

.introjs-button:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.introjs-skipbutton {
    display: inline-block !important;
    background: var(--surface, #fff) !important;
    color: var(--text-muted) !important;
    /* border: 1.5px solid var(--primary-color, #2563eb) !important; */
    border-radius: var(--radius-md, 6px) !important;
    padding: 4px 16px !important;
    font-size: 0.8em !important;
    font-weight: 400 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    box-sizing: border-box !important;
    min-width: 0 !important;
    max-width: none !important;
    width: auto !important;
    text-align: center !important;
    cursor: pointer !important;
    transition: background 0.2s, color 0.2s;
}

.introjs-progressbar {
    background: var(--primary-color) !important;
}

.introjs-step-number {
    color: var(--primary-color);
    font-weight: 500;
    font-size: 0.95em;
    margin-bottom: 8px;
    text-align: center;
}

.congrats-modal {
    position: relative;
    overflow: hidden;
}
.congrats-animation {
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    width: 100%; height: 100%;
    pointer-events: none;
    z-index: 1;
}
.congrats-content, .modal-header, .modal-footer {
    position: relative;
    z-index: 2;
}

.confetti-popper {
    position: absolute;
    bottom: 0;
    width: 0;
    height: 0;
    pointer-events: none;
}
.confetti-popper.left {
    left: 30px;
}
.confetti-popper.right {
    right: 30px;
}
.confetti {
    position: absolute;
    bottom: 0;
    width: 12px;
    height: 18px;
    border-radius: 3px;
    opacity: 0.85;
    animation: confetti-explode 1.2s cubic-bezier(0.22, 1, 0.36, 1) forwards;
}
.confetti-popper.left .confetti:nth-child(1) { left: 0; background: #FFD700; animation-delay: 0s;    transform: rotate(-20deg);}
.confetti-popper.left .confetti:nth-child(2) { left: 10px; background: #FF69B4; animation-delay: 0.1s; transform: rotate(-10deg);}
.confetti-popper.left .confetti:nth-child(3) { left: 20px; background: #00CFFF; animation-delay: 0.2s; transform: rotate(0deg);}
.confetti-popper.left .confetti:nth-child(4) { left: 30px; background: #52FF00; animation-delay: 0.15s; transform: rotate(10deg);}
.confetti-popper.left .confetti:nth-child(5) { left: 40px; background: #FF8C00; animation-delay: 0.05s; transform: rotate(20deg);}
.confetti-popper.right .confetti:nth-child(1) { right: 0; background: #FFD700; animation-delay: 0s;    transform: rotate(20deg);}
.confetti-popper.right .confetti:nth-child(2) { right: 10px; background: #FF69B4; animation-delay: 0.1s; transform: rotate(10deg);}
.confetti-popper.right .confetti:nth-child(3) { right: 20px; background: #00CFFF; animation-delay: 0.2s; transform: rotate(0deg);}
.confetti-popper.right .confetti:nth-child(4) { right: 30px; background: #52FF00; animation-delay: 0.15s; transform: rotate(-10deg);}
.confetti-popper.right .confetti:nth-child(5) { right: 40px; background: #FF8C00; animation-delay: 0.05s; transform: rotate(-20deg);}
@keyframes confetti-explode {
    0% {
        opacity: 0.85;
        transform: translateY(0) scale(1);
    }
    80% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: translateY(-220px) scale(1.1);
    }
}

.congrats-animation .confetti:nth-child(1)  { left: 3%;  background: #FFD700;  animation-delay: 0.00s; transform: rotate(-22deg);}
.congrats-animation .confetti:nth-child(2)  { left: 12%; background: #FF69B4;  animation-delay: 0.09s; transform: rotate(15deg);}
.congrats-animation .confetti:nth-child(3)  { left: 7%;  background: #00CFFF;  animation-delay: 0.04s; transform: rotate(-8deg);}
.congrats-animation .confetti:nth-child(4)  { left: 28%; background: #52FF00;  animation-delay: 0.13s; transform: rotate(7deg);}
.congrats-animation .confetti:nth-child(5)  { left: 19%; background: #FF8C00;  animation-delay: 0.07s; transform: rotate(-17deg);}
.congrats-animation .confetti:nth-child(6)  { left: 23%; background: #FFD700;  animation-delay: 0.11s; transform: rotate(12deg);}
.congrats-animation .confetti:nth-child(7)  { left: 35%; background: #FF69B4;  animation-delay: 0.02s; transform: rotate(-12deg);}
.congrats-animation .confetti:nth-child(8)  { left: 41%; background: #00CFFF;  animation-delay: 0.15s; transform: rotate(18deg);}
.congrats-animation .confetti:nth-child(9)  { left: 48%; background: #52FF00;  animation-delay: 0.06s; transform: rotate(-5deg);}
.congrats-animation .confetti:nth-child(10) { left: 53%; background: #FF8C00;  animation-delay: 0.10s; transform: rotate(20deg);}
.congrats-animation .confetti:nth-child(11) { left: 61%; background: #FFD700;  animation-delay: 0.03s; transform: rotate(-18deg);}
.congrats-animation .confetti:nth-child(12) { left: 67%; background: #FF69B4;  animation-delay: 0.12s; transform: rotate(9deg);}
.congrats-animation .confetti:nth-child(13) { left: 72%; background: #00CFFF;  animation-delay: 0.08s; transform: rotate(-14deg);}
.congrats-animation .confetti:nth-child(14) { left: 77%; background: #52FF00;  animation-delay: 0.14s; transform: rotate(16deg);}
.congrats-animation .confetti:nth-child(15) { left: 81%; background: #FF8C00;  animation-delay: 0.05s; transform: rotate(-10deg);}
.congrats-animation .confetti:nth-child(16) { left: 86%; background: #FFD700;  animation-delay: 0.01s; transform: rotate(13deg);}
.congrats-animation .confetti:nth-child(17) { left: 91%; background: #FF69B4;  animation-delay: 0.16s; transform: rotate(-7deg);}
.congrats-animation .confetti:nth-child(18) { left: 95%; background: #00CFFF;  animation-delay: 0.07s; transform: rotate(11deg);}
.congrats-animation .confetti:nth-child(19) { left: 97%; background: #52FF00;  animation-delay: 0.09s; transform: rotate(-15deg);}
.congrats-animation .confetti:nth-child(20) { left: 99%; background: #FF8C00;  animation-delay: 0.13s; transform: rotate(19deg);}

@keyframes confetti-explode {
    0% {
        opacity: 0.85;
        transform: translateY(0) scale(1);
    }
    80% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        /* Each confetti flies up and toward the center */
        transform: translateY(-220px) translateX(-50%) scale(1.1);
    }
}

select.form-input,
select.edit-input {
    cursor: pointer !important;
}

select,
select option {
    color: var(--text-primary) !important;
}

input::placeholder,
textarea::placeholder {
    color: var(--text-muted) !important;
    opacity: 1;
}

select option[value=""] {
    color: var(--text-muted) !important;
}

select:focus,
select:valid {
    color: var(--text-primary) !important;
}

select:invalid {
    color: var(--text-muted) !important;
}

input:required:invalid,
select:required:invalid,
textarea:required:invalid {
  border-color: var(--error-color) !important;
  box-shadow: none !important;
}

.toast-error {
  background: var(--error-color, #ef4444);
  color: #fff;
  border-color: var(--error-color, #ef4444);
}
.toast-success {
  background: var(--success-color, #22c55e);
  color: #fff;
  border-color: var(--success-color, #22c55e);
}
.toast-info {
  background: var(--primary-color, #2563eb);
  color: #fff;
  border-color: var(--primary-color, #2563eb);
}

.toast {
  position: relative;
}

.toast .message-close {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.1em;
  position: absolute;
  top: 8px;
  right: 12px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
  padding: 0;
  line-height: 1;
}

.toast .message-close:hover {
  opacity: 1;
  color: #fff;
}

.country-code-select {
  display: inline-flex;
  width: 130px !important;
  padding-right: 1em;
  margin-right: 0.5em;
  vertical-align: middle;
}

.page-header{
    margin-bottom: var(--space-md);
}

@media (max-width: 992px) {
    .btn, .btn-action {
        height: 40px;
        font-size: 0.93rem;
        padding: 0.35rem 0.85rem;
        min-width: 32px;
    }
    .btn-sm {
        height: 32px;
        font-size: 0.9rem;
        padding: 0.28rem 0.7rem;
        min-width: 28px;
    }
    .btn-lg {
        height: 48px;
        font-size: 1rem;
        padding: 0.45rem 1.2rem;
        min-width: 36px;
    }
}

/* Spinner styles for loading indicators */
.spinner-border {
    display: inline-block;
    width: 1.5rem;
    height: 1.5rem;
    vertical-align: text-bottom;
    border: 0.2em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.15em;
}

@keyframes spinner-border {
    to { transform: rotate(360deg); }
}

.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

@media (max-width: 768px) {
    .btn, .btn-action {
        height: 36px;
        font-size: 0.9rem;
        padding: 0.28rem 0.7rem;
        min-width: 28px;
    }
    .btn-sm {
        height: 28px;
        font-size: 0.85rem;
        padding: 0.18rem 0.5rem;
        min-width: 24px;
    }
    .btn-lg {
        height: 44px;
        font-size: 0.95rem;
        padding: 0.38rem 1rem;
        min-width: 32px;
    }
}

@media (max-width: 480px) {
    .btn, .btn-action {
        height: 32px;
        font-size: 0.85rem;
        padding: 0.18rem 0.5rem;
        min-width: 24px;
    }
    .btn-sm {
        height: 24px;
        font-size: 0.8rem;
        padding: 0.12rem 0.4rem;
        min-width: 20px;
    }
    .btn-lg {
        height: 38px;
        font-size: 0.9rem;
        padding: 0.28rem 0.8rem;
        min-width: 28px;
    }
}

.status-pill {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 16px;
  border-radius: 16px;
  font-size: var(--font-size-sm);
  font-weight: 500;
  background: #f4f4f4;
  color: #3b82f6;
  margin: 0 4px;
  letter-spacing: 0.02em;
  transition: background 0.2s, color 0.2s;
  border: none;
  box-shadow: none;
}

.status-pill.status-blue    { background: #e3f0ff; color: #3b82f6; }
.status-pill.status-green   { background: #e6faea; color: #22c55e; }
.status-pill.status-yellow  { background: #fff7e0; color: #eab308; }
.status-pill.status-red     { background: #ffeaea; color: #ef4444; }
.status-pill.status-purple  { background: #ede9fe; color: #6366f1; }
.status-pill.status-pink    { background: #ffe4f0; color: #ec4899; }
.status-pill.status-cyan    { background: #e0f7fa; color: #06b6d4; }

.status-icons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.status-icons i {
  font-size: 1.2em;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 18px 4px 12px;
  border-radius: 24px;
  font-size: var(--font-size-xs);
  font-weight: 400;
  border: 2px solid transparent;
  background: #fff;
  transition: border 0.2s, background 0.2s, color 0.2s;
  box-shadow: none;
}
.status-badge i {
  font-size: 1.1em;
  margin-right: 6px;
}
.status-connected {
  color: #15803d;
  background: #f0fdf4;
  border-color: #bbf7d0;
}
.status-importing {
  color: #eab308;
  background: #fffcd6;
  border-color: #fde68a;
}
.status-disabled {
  color: #52525b;
  background: #f4f4f5;
  border-color: #e4e4e7;
}
.status-attention {
  color: #b91c1c;
  background: #fef2f2;
  border-color: #fecaca;
}

.custom-scrollbar {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    overflow-y: auto;
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}
.custom-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.compact-checkbox-group .standard-checkbox {
    margin-bottom: 0.25rem;
}
.compact-checkbox-group .standard-checkbox:last-child {
    margin-bottom: 0;
}

.custom-tour-tooltip {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--border-color) !important;
  background: var(--surface) !important;
  color: var(--text-primary) !important;
  font-size: 1rem !important;
  padding: var(--space-lg) !important;
  max-width: 350px !important;
}
.custom-tour-tooltip .introjs-tooltip-title {
  color: var(--primary-color) !important;
  font-weight: 600 !important;
  font-size: 1.1em !important;
  margin-bottom: var(--space-sm) !important;
}
.custom-tour-tooltip .introjs-tooltiptext {
  color: var(--text-secondary) !important;
  font-size: 1em !important;
}
.custom-tour-tooltip .introjs-button {
  background: var(--primary-color) !important;
  color: var(--text-inverse) !important;
  border-radius: var(--radius-md) !important;
  font-weight: 500 !important;
  font-size: 1em !important;
  padding: 8px 18px !important;
  margin: 0 4px !important;
  border: none !important;
  box-shadow: var(--shadow-sm) !important;
  transition: background 0.2s;
}
.custom-tour-tooltip .introjs-button:hover {
  background: var(--primary-hover) !important;
}
.custom-tour-tooltip .introjs-skipbutton {
  background: var(--surface) !important;
  color: var(--text-muted) !important;
  border-radius: var(--radius-md) !important;
  font-size: 0.95em !important;
  font-weight: 400 !important;
  padding: 4px 16px !important;
  border: none !important;
}
.custom-tour-tooltip .introjs-progressbar {
  background: var(--primary-color) !important;
}
.custom-tour-tooltip .introjs-step-number {
  color: var(--primary-color) !important;
  font-weight: 600 !important;
  font-size: 1em !important;
}
.custom-tour-highlight {
  box-shadow: 0 0 0 4px var(--primary-color), 0 2px 8px rgba(30,41,59,0.10) !important;
  border-radius: var(--radius-md) !important;
  transition: box-shadow 0.2s;
}

.customTooltip {
    background: var(--surface);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    line-height: 1.5;
    max-width: 350px;
    padding: 2rem 2.2rem;
}
.customHighlight {
    background: transparent !important;
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.3) !important;
    border-radius: var(--radius-lg) !important;
    transition: all 0.3s ease !important;
}
[data-theme="dark"] .customTooltip {
    background: var(--surface);
    border-color: var(--border-color);
}
[data-theme="dark"] .customHighlight {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.4) !important;
}

.pagetour-tooltip {
    background: #fff;
    color: #222;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(30,41,59,0.10), 0 1.5px 6px rgba(30,41,59,0.06);
    border: none;
    padding: 2rem 2.2rem 1.2rem 2.2rem;
    max-width: 400px;
    min-width: 320px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    z-index: 9999999 !important;
}
.pagetour-tooltip .introjs-tooltip-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #222;
    margin-bottom: 0.5rem;
}
.pagetour-tooltip .introjs-tooltiptext {
    color: #444;
    font-size: 1rem;
    margin-bottom: 1.2rem;
}
.pagetour-tooltip .introjs-progressbar {
    background: var(--primary-color, #6366f1);
    height: 5px;
    border-radius: 4px;
    margin-bottom: 1.2rem;
}
.pagetour-tooltip .introjs-button {
    background: var(--primary-color, #6366f1);
    color: #fff;
    border-radius: 8px;
    font-weight: 500;
    font-size: 1rem;
    padding: 8px 24px;
    border: none;
    margin: 0 4px;
    box-shadow: 0 1px 4px rgba(30,41,59,0.06);
    transition: background 0.2s;
}
.pagetour-tooltip .introjs-button:hover {
    background: var(--primary-hover, #5856eb);
}
.pagetour-tooltip .introjs-skipbutton {
    background: none;
    color: #888;
    font-size: 0.95em;
    font-weight: 400;
    border: none;
    position: absolute;
    top: 1.1rem;
    right: 1.5rem;
    padding: 0;
    cursor: pointer;
    text-decoration: underline;
}
.pagetour-tooltip .introjs-tooltipbuttons {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1.2rem;
}
.pagetour-tooltip .introjs-tooltip {
    box-shadow: none;
    border: none;
}
.pagetour-tooltip .introjs-progress {
    margin-bottom: 1rem;
}

.driver-popover {
    background: #fff !important;
    color: #222 !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 24px rgba(30,41,59,0.10), 0 1.5px 6px rgba(30,41,59,0.06) !important;
    border: none !important;
    padding: 1.5rem 2rem 1.2rem 2rem !important;
    min-width: 320px;
    max-width: 400px;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    line-height: 1.6;
}
.driver-popover-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #222;
}
.driver-popover-description {
    color: #444;
    font-size: 1rem;
    margin-bottom: 1.2rem;
}
.driver-popover-arrow {
    display: block;
}

.driver-popover-footer button {
    border-radius: 6px !important;
    font-size: 1rem !important;
    padding: 0.5rem 1.2rem !important;
    font-weight: 500 !important;
    border: none !important;
    cursor: pointer !important;
}
.driver-popover-next-btn {
    background: var(--primary-color) !important;
    color: #fff !important;
    font-weight: 300 !important;
    transition: background 0.2s !important;
}
.driver-popover-next-btn:hover {
    background: var(--primary-hover) !important;
}
.driver-popover-prev-btn {
    background: #f3f3f3 !important;
    color: #222 !important;
    font-weight: 300 !important;
    border: 1px solid #ddd !important;
}
.driver-popover-footer .btn-outline:hover {
    background: #e5e5e5 !important;
}

.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

@media (max-width: 991.98px) {
    .table-responsive .data-table {
        width: 150%;
        min-width: 600px;
    }
}

/* Uiverse.io Dots Spinner for Loading Overlay */
.loading-overlay .dots {
  width: var(--size);
  height: var(--size);
  position: relative;
}
.loading-overlay .dot{
  width: var(--size);
  height: var(--size);
  animation: dwl-dot-spin calc(var(--speed) * 5) infinite linear both;
  animation-delay: calc(var(--i) * var(--speed) / (var(--dot-count) + 2) * -1);
  rotate: calc(var(--i) * var(--spread) / (var(--dot-count) - 1));
  position: absolute;
}
.loading-overlay .dot::before {
  content: "";
  display: block;
  width: var(--dot-size);
  height: var(--dot-size);
  background-color: var(--color);
  border-radius: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
  bottom: 0;
  left: 50%;
}
@keyframes dwl-dot-spin {
  0% {
    transform: rotate(0deg);
    animation-timing-function: cubic-bezier(0.390, 0.575, 0.565, 1.000);
    opacity: 1;
  }
  2% {
    transform: rotate(20deg);
    animation-timing-function: linear;
    opacity: 1;
  }
  30% {
    transform: rotate(180deg);
    animation-timing-function: cubic-bezier(0.445, 0.050, 0.550, 0.950);
    opacity: 1;
  }
  41% {
    transform: rotate(380deg);
    animation-timing-function: linear;
    opacity: 1;
  }
  69% {
    transform: rotate(520deg);
    animation-timing-function: cubic-bezier(0.445, 0.050, 0.550, 0.950);
    opacity: 1;
  }
  76% {
    opacity: 1;
  }
  76.1% {
    opacity: 0;
  }
  80% {
    transform: rotate(720deg);
  }
  100% {
    opacity: 0;
  }
}

/* Uiverse.io Dots Spinner for Page Loading Overlay */
.page-loading-spinner .dots {
  width: var(--size);
  height: var(--size);
  position: relative;
}
.page-loading-spinner .dot {
  width: var(--size);
  height: var(--size);
  animation: dwl-dot-spin calc(var(--speed) * 5) infinite linear both;
  animation-delay: calc(var(--i) * var(--speed) / (var(--dot-count) + 2) * -1);
  rotate: calc(var(--i) * var(--spread) / (var(--dot-count) - 1));
  position: absolute;
}
.page-loading-spinner .dot::before {
  content: "";
  display: block;
  width: var(--dot-size);
  height: var(--dot-size);
  background-color: var(--color);
  border-radius: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
  bottom: 0;
  left: 50%;
}

.offcanvas {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 600px;
  max-width: 100vw;
  background: #fff;
  box-shadow: -2px 0 16px rgba(0,0,0,0.12);
  z-index: 1200;
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  flex-direction: column;
}

.offcanvas.show {
  transform: translateX(0);
}

.offcanvas-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.offcanvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md);
  background: var(--surface);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
  font-size: 1.15rem;
  font-weight: 600;
  letter-spacing: 0.01em;
}

.modal-header {
    padding: var(--space-md);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    background: var(--surface);
    z-index: 10;
}


.offcanvas-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted);
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  transition: background 0.2s, color 0.2s;
  line-height: 1;
  margin-left: 0.5rem;
}
.offcanvas-close:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
}

.offcanvas-body {
  flex: 1 1 auto;
  overflow-y: auto;
  padding: 2rem;
}

@media (max-width: 600px) {
  .offcanvas {
    width: 100vw;
    max-width: 100vw;
  }
  .offcanvas-body {
    padding: 1rem;
  }
  .offcanvas-header {
    padding: 1rem;
  }
}

.empty-state.center-offcanvas {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  text-align: center;
}

.filter-type-suboptions {
    display: none;
    overflow: hidden;
    padding: 0 var(--space-md);
    transition: max-height 0.3s cubic-bezier(0.4,0,0.2,1);
    max-height: 0;
    gap: 0.5rem;
}
.filter-type-suboptions.active,
.filter-type-suboptions.show {
    display: block;
    max-height: 100px;
}

.filter-type-suboptions-box {
    border: 1px solid var(--border-color, #e5e7eb);
    border-radius: var(--radius-md, 8px);
    background: var(--surface, #fff);
    padding: 0.75rem 1rem;
    margin-top: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.import-type-btn-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    margin: var(--space-md) 0.5rem 0 0.5rem;
}
.import-type-btn {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    background: var(--surface, #fff);
    border: 1px solid var(--border-color, #e5e7eb);
    border-radius: var(--radius-md, 8px);
    padding: var(--space-sm) var(--space-md);
    font-size: var(--font-size-md);
    color: var(--text, #222);
    cursor: pointer;
    transition: background 0.2s, border 0.2s, color 0.2s;
    outline: none;
}
.import-type-btn:hover, .import-type-btn.active {
    background: none;
    color: var(--primary-color);
    border-color: var(--primary-light, #60a5fa);
}
.import-type-btn i {
    font-size: 1.1em;
}

