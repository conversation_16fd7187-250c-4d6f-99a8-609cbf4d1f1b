class AdminFinanceModule {
    constructor() {
        this.currentPage = 1;
        this.currentTab = 'loans';
        this.selectedFiles = [];
        this.employeesData = [];
        this.isLoading = false;
        this.chart = null;
        this.currentChartPeriod = 'month';
        this.currentChartType = 'line';
        this.currentFilterCategory = 'loans';
        this.currentFilterType = '';
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupTabSwitching();
        // this.setupFileUpload(); // Disabled old file upload logic
        this.loadEmployeesData();
        
        // Load default chart data with first loan type
        this.loadDefaultChartData();
        
        // Initialize chart after a short delay to ensure DOM is ready
        this.initChartWithRetry();
    }

    initChartWithRetry(retryCount = 0) {
        const maxRetries = 5;
        
        if (retryCount >= maxRetries) {
            console.error('Failed to initialize chart after maximum retries');
            return;
        }

        const ctx = document.getElementById('financeChart');
        if (!ctx) {
            console.log(`Canvas not found, retrying in 200ms (attempt ${retryCount + 1}/${maxRetries})`);
            setTimeout(() => this.initChartWithRetry(retryCount + 1), 200);
            return;
        }

        // Check if the canvas is properly connected to the DOM
        if (!ctx.ownerDocument || !ctx.ownerDocument.body.contains(ctx)) {
            console.log(`Canvas not connected to DOM, retrying in 200ms (attempt ${retryCount + 1}/${maxRetries})`);
            setTimeout(() => this.initChartWithRetry(retryCount + 1), 200);
            return;
        }

        // If all checks pass, initialize the chart
        this.initChart();
    }

    async loadDefaultChartData() {
        try {
            // Load loan types to get the first one
            const response = await fetch('/finance/filter-options/?category=loans');
            const data = await response.json();
            
            if (data.success && data.options.length > 0) {
                // Set the first loan type as default
                this.currentFilterType = data.options[0];
                console.log('Default loan type set:', this.currentFilterType);
                
                // Load chart data with default values
                setTimeout(() => {
                    this.loadChartData();
                }, 500);
            }
        } catch (error) {
            console.error('Error loading default chart data:', error);
        }
    }

    initializeFilterPopover() {
        const categorySelect = document.getElementById('filterCategorySelect');
        if (categorySelect) {
            // Set the current category
            categorySelect.value = this.currentFilterCategory || 'loans';
            
            // Trigger the change event to load the type options
            const event = new Event('change');
            categorySelect.dispatchEvent(event);
            
            // Set the current filter type after options are loaded
            setTimeout(() => {
                const typeRadios = document.querySelectorAll('input[name="filter_type"]');
                typeRadios.forEach(radio => {
                    if (radio.value === this.currentFilterType) {
                        radio.checked = true;
                    }
                });
            }, 100);
        }
    }
    
    setupEventListeners() {
        // View employee details buttons
        document.addEventListener('click', (e) => {
            // Only handle if it's a .view-employee-btn button, not an anchor
            const btn = e.target.closest('.view-employee-btn');
            if (btn && btn.tagName !== 'A') {
                e.preventDefault();
                const employeeId = btn.dataset.employeeId;
                const dataType = btn.dataset.type;
                this.showEmployeeDetails(employeeId, dataType);
            }
            
            // Pagination buttons
            if (e.target.closest('.pagination-btn')) {
                const btn = e.target.closest('.pagination-btn');
                const page = parseInt(btn.dataset.page);
                if (!isNaN(page)) {
                    this.loadEmployeesData(page);
                }
            }
        });
        
        // Search inputs
        const searchInputs = ['#loan-employee-search', '#allowance-employee-search'];
        searchInputs.forEach(selector => {
            const input = document.querySelector(selector);
            if (input) {
                input.addEventListener('input', this.debounce(() => {
                    this.loadEmployeesData(1);
                }, 300));
            }
        });
        
        // Department filters
        const departmentFilters = ['#loan-department-filter', '#allowance-department-filter'];
        departmentFilters.forEach(selector => {
            const select = document.querySelector(selector);
            if (select) {
                select.addEventListener('change', () => {
                    this.loadEmployeesData(1);
                });
            }
        });
        
        // Employee type change for file upload
        const employeeTypeSelect = document.getElementById('employee-type-select');
        if (employeeTypeSelect) {
            employeeTypeSelect.addEventListener('change', (e) => {
                this.updateFileUploadHint(e.target.value);
            });
        }
        
        // Form submissions
        const payslipForm = document.getElementById('payslip-upload-form');
        const loansForm = document.getElementById('loans-upload-form');
        const allowancesForm = document.getElementById('allowances-upload-form');
        
        // [REMOVE LEGACY PAYSPLIP UPLOAD HANDLER]
        // if (payslipForm) {
        //     payslipForm.addEventListener('submit', (e) => {
        //         e.preventDefault();
        //         this.handleFormSubmission('payslip');
        //     });
        // }
        // [END REMOVE]
        
        if (loansForm) {
            loansForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmission('loans');
            });
        }
        
        if (allowancesForm) {
            allowancesForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmission('allowances');
            });
        }
        
        // Modal and offcanvas close events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('offcanvas-overlay')) {
                const offcanvas = e.target.closest('.offcanvas');
                if (offcanvas) this.closeOffcanvas(offcanvas.id);
            }
        });
        
        // ESC key handlers
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openOffcanvas = document.querySelector('.offcanvas.show');
                if (openOffcanvas) this.closeOffcanvas(openOffcanvas.id);
            }
        });

        // Cancel import buttons
        const cancelButtons = [
            'cancelPayslipImportBtn',
            'cancelLoanImportBtn', 
            'cancelAllowanceImportBtn'
        ];
        
        cancelButtons.forEach(btnId => {
            const btn = document.getElementById(btnId);
            if (btn) {
                btn.addEventListener('click', () => {
                    this.resetForm(btnId.replace('cancel', '').replace('ImportBtn', ''));
                });
            }
        });

        // Template download buttons
        const templateButtons = [
            'exportPayslipTemplateBtn',
            'exportLoanTemplateBtn',
            'exportAllowanceTemplateBtn'
        ];
        
        templateButtons.forEach(btnId => {
            const btn = document.getElementById(btnId);
            if (btn) {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.downloadTemplate(btnId.replace('export', '').replace('TemplateBtn', ''));
                });
            }
        });

        // Filter popover toggle
        const filterBtn = document.getElementById('filterBtn');
        const filterPopover = document.getElementById('filterPopover');
        if (filterBtn && filterPopover) {
            console.log('Filter button and popover found');
            
            // Remove existing event listeners to prevent duplicates
            const newFilterBtn = filterBtn.cloneNode(true);
            filterBtn.parentNode.replaceChild(newFilterBtn, filterBtn);
            
            newFilterBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                console.log('Filter button clicked');
                filterPopover.classList.toggle('show');
                console.log('Filter popover show class:', filterPopover.classList.contains('show'));
                
                // Initialize filter popover with current values when opened
                if (filterPopover.classList.contains('show')) {
                    this.initializeFilterPopover();
                }
            });
            
            // Remove existing document event listeners
            const existingClickHandler = this._documentClickHandler;
            const existingKeydownHandler = this._documentKeydownHandler;
            
            if (existingClickHandler) {
                document.removeEventListener('click', existingClickHandler);
            }
            if (existingKeydownHandler) {
                document.removeEventListener('keydown', existingKeydownHandler);
            }
            
            // Create new handlers and store references
            this._documentClickHandler = (e) => {
                if (!filterPopover.contains(e.target) && e.target !== newFilterBtn) {
                    filterPopover.classList.remove('show');
                }
            };
            
            this._documentKeydownHandler = (e) => {
                if (e.key === 'Escape') {
                    filterPopover.classList.remove('show');
                }
            };
            
            document.addEventListener('click', this._documentClickHandler);
            document.addEventListener('keydown', this._documentKeydownHandler);
        } else {
            console.log('Filter button or popover not found:', { filterBtn, filterPopover });
        }

        // Import popover toggle
        const importBtn = document.getElementById('importBtn');
        const importPopover = document.getElementById('importPopover');
        if (importBtn && importPopover) {
            importBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                importPopover.classList.toggle('show');
            });
            
            // Close import popover when clicking outside
            document.addEventListener('click', (e) => {
                if (!importPopover.contains(e.target) && e.target !== importBtn) {
                    importPopover.classList.remove('show');
                }
            });
            
            // Close import popover on ESC key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    importPopover.classList.remove('show');
                }
            });
        }

        // Add global applyFilter function
        window.applyFilter = (event) => {
            event.preventDefault();
            const form = event.target;
            const categorySelect = form.querySelector('#filterCategorySelect');
            const typeRadios = form.querySelectorAll('input[name="filter_type"]');
            
            let selectedType = '';
            typeRadios.forEach(radio => {
                if (radio.checked) {
                    selectedType = radio.value;
                }
            });
            
            const filterData = {
                category: categorySelect.value,
                type: selectedType
            };
            
            console.log('Applying filter:', filterData);
            
            // Update chart filter
            if (window.adminFinance) {
                window.adminFinance.currentFilterCategory = filterData.category;
                window.adminFinance.currentFilterType = filterData.type;
                window.adminFinance.loadChartData();
            }
            
            // Close the popover
            const filterPopover = document.getElementById('filterPopover');
            if (filterPopover) {
                filterPopover.classList.remove('show');
            }
            
            this.showToast('Filter applied successfully', 'success');
        };

        // Add global closeFilterPopover function
        window.closeFilterPopover = () => {
            const filterPopover = document.getElementById('filterPopover');
            if (filterPopover) {
                filterPopover.classList.remove('show');
            }
        };

        // Add global import functions
        window.closeImportPopover = () => {
            const importPopover = document.getElementById('importPopover');
            if (importPopover) {
                importPopover.classList.remove('show');
            }
        };



        // Add global chart control functions
        window.updateFinanceChartPeriod = (period) => {
            if (window.adminFinance) {
                window.adminFinance.updateChartPeriod(period);
            }
        };

        window.switchFinanceChartType = (type) => {
            if (window.adminFinance) {
                window.adminFinance.switchChartType(type);
            }
        };

        // Add global filter options function
        window.updateFilterOptions = async () => {
            const categorySelect = document.getElementById('filterCategorySelect');
            const typeSection = document.getElementById('filterTypeSection');
            const typeOptions = document.getElementById('filterTypeOptions');
            
            if (!categorySelect || !typeSection || !typeOptions) return;
            
            const category = categorySelect.value;
            
            if (!category) {
                typeSection.style.display = 'none';
                return;
            }
            
            try {
                const response = await fetch(`/finance/filter-options/?category=${category}`);
                const data = await response.json();
                
                if (data.success) {
                    typeOptions.innerHTML = '';
                    
                    data.options.forEach((option, index) => {
                        const optionDiv = document.createElement('div');
                        optionDiv.className = 'filter-type-option';
                        optionDiv.innerHTML = `
                            <input type="radio" name="filter_type" value="${option}" id="type_${option}" ${index === 0 ? 'checked' : ''}>
                            <label for="type_${option}">${option}</label>
                        `;
                        typeOptions.appendChild(optionDiv);
                    });
                    
                    typeSection.style.display = 'block';
                    
                    // Set the current filter type if it exists in the options
                    if (window.adminFinance && window.adminFinance.currentFilterType) {
                        const currentTypeRadio = document.querySelector(`input[name="filter_type"][value="${window.adminFinance.currentFilterType}"]`);
                        if (currentTypeRadio) {
                            currentTypeRadio.checked = true;
                        }
                    }
                }
            } catch (error) {
                console.error('Error loading filter options:', error);
            }
        };

        // Add filter condition change handler
        const filterConditionRadios = document.querySelectorAll('input[name="filter_condition"]');
        const filterValueInput = document.getElementById('filterValueInput');
        
        if (filterConditionRadios.length > 0 && filterValueInput) {
            filterConditionRadios.forEach(radio => {
                radio.addEventListener('change', () => {
                    if (radio.value === 'any') {
                        filterValueInput.style.display = 'none';
                        filterValueInput.value = '';
                    } else {
                        filterValueInput.style.display = 'block';
                    }
                });
            });
        }
    }
    
    setupTabSwitching() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.dataset.tab;
                
                // Remove active class from all tabs and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                button.classList.add('active');
                const targetContent = document.getElementById(targetTab);
                if (targetContent) {
                    targetContent.classList.add('active');
                    
                    // Load data if needed
                    if (targetTab.includes('admin')) {
                        this.currentTab = targetTab.replace('-admin', '');
                        this.loadEmployeesData(1);
                    }
                }
            });
        });
    }
    
    setupFileUpload() {
        // Setup payslip upload
        this.setupUploadArea('payslip-upload-area', 'payslip-files', 'payslip-file-list', 'selectedPayslipFiles', 'payslip-upload-btn');
        
        // Setup loans upload
        this.setupUploadArea('loans-upload-area', 'loans-files', 'loans-file-list', 'selectedLoanFiles', 'loans-upload-btn');
        
        // Setup allowances upload
        this.setupUploadArea('allowances-upload-area', 'allowances-files', 'allowances-file-list', 'selectedAllowanceFiles', 'allowances-upload-btn');
    }
    
    setupUploadArea(areaId, inputId, listId, selectedFilesId, uploadBtnId) {
        const fileInput = document.getElementById(inputId);
        const fileUploadArea = document.getElementById(areaId);
        const fileList = document.getElementById(listId);
        const selectedFilesContainer = document.getElementById(selectedFilesId);
        const uploadBtn = document.getElementById(uploadBtnId);
        
        if (!fileInput || !fileUploadArea) return;
        
        // Drag and drop
        fileUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        });
        
        fileUploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
        });
        
        fileUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            
            const files = Array.from(e.dataTransfer.files);
            this.handleFileSelection(files, listId, selectedFilesContainer, uploadBtn);
        });
        
        // File input change
        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleFileSelection(files, listId, selectedFilesContainer, uploadBtn);
        });
    }
    
    handleFileSelection(files, listId, selectedFilesContainer, uploadBtn) {
        const validExtensions = ['.pdf', '.jpg', '.jpeg', '.png', '.xlsx', '.xls', '.csv'];
        
        files.forEach(file => {
            const ext = '.' + file.name.split('.').pop().toLowerCase();
            if (validExtensions.includes(ext)) {
                if (!this.selectedFiles.find(f => f.name === file.name)) {
                    this.selectedFiles.push(file);
                }
            } else {
                this.showToast(`Invalid file type: ${file.name}`, 'error');
            }
        });
        
        this.updateFileList(listId, selectedFilesContainer, uploadBtn);
    }
    
    updateFileList(listId, selectedFilesContainer, uploadBtn) {
        const fileList = document.getElementById(listId);
        if (!fileList) return;
        
        fileList.innerHTML = '';
        
        this.selectedFiles.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <i class="fas fa-file file-icon"></i>
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">(${this.formatFileSize(file.size)})</span>
                </div>
                <button type="button" class="file-remove" onclick="adminFinance.removeFile(${index}, '${listId}', '${selectedFilesContainer}', '${uploadBtn}')">
                    <i class="fas fa-times"></i>
                </button>
            `;
            fileList.appendChild(fileItem);
        });
        
        // Show/hide selected files container and enable/disable upload button
        if (selectedFilesContainer) {
            selectedFilesContainer.style.display = this.selectedFiles.length > 0 ? 'block' : 'none';
        }
        
        if (uploadBtn) {
            uploadBtn.disabled = this.selectedFiles.length === 0;
        }
    }
    
    removeFile(index, listId, selectedFilesContainer, uploadBtn) {
        this.selectedFiles.splice(index, 1);
        this.updateFileList(listId, selectedFilesContainer, uploadBtn);
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    updateFileUploadHint(employeeType) {
        const hint = document.getElementById('file-format-hint');
        if (!hint) return;
        
        if (employeeType === 'ojt') {
            hint.textContent = 'Select XLSX file for OJT employees';
        } else {
            hint.textContent = 'Select PDF/JPG/PNG files for Regular employees';
        }
    }

    downloadTemplate(type) {
        const templates = {
            'Payslip': '/finance/template/payslip/',
            'Loan': '/finance/template/loan/',
            'Allowance': '/finance/template/allowance/'
        };
        
        const url = templates[type];
        if (url) {
            window.open(url, '_blank');
        } else {
            this.showToast('Template not available', 'error');
        }
    }
    
    handleFormSubmission(type) {
        let form, uploadBtn, listId;
        
        switch (type) {
            case 'payslip':
                form = document.getElementById('payslip-upload-form');
                uploadBtn = document.getElementById('payslip-upload-btn');
                listId = 'payslip-file-list';
                break;
            case 'loans':
                form = document.getElementById('loans-upload-form');
                uploadBtn = document.getElementById('loans-upload-btn');
                listId = 'loans-file-list';
                break;
            case 'allowances':
                form = document.getElementById('allowances-upload-form');
                uploadBtn = document.getElementById('allowances-upload-btn');
                listId = 'allowances-file-list';
                break;
            default:
                return;
        }
        
        if (!form || this.selectedFiles.length === 0) {
            this.showToast('Please select files to upload', 'warning');
            return;
        }
        
        // Create FormData and append selected files
        const formData = new FormData(form);
        
        // Remove the original file input files
        formData.delete('files');
        
        // Add our selected files
        this.selectedFiles.forEach(file => {
            formData.append('files', file);
        });
        
        // Show loading state
        if (uploadBtn) {
            uploadBtn.classList.add('btn-loading');
            uploadBtn.disabled = true;
        }
        
        // Submit form
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': this.getCSRFToken()
            }
        })
        .then(response => {
            if (response.ok) {
                this.showToast(`${type.charAt(0).toUpperCase() + type.slice(1)} uploaded successfully`, 'success');
                this.resetForm(type);
            } else {
                this.showToast(`${type.charAt(0).toUpperCase() + type.slice(1)} upload failed`, 'error');
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            this.showToast(`${type.charAt(0).toUpperCase() + type.slice(1)} upload failed`, 'error');
        })
        .finally(() => {
            if (uploadBtn) {
                uploadBtn.classList.remove('btn-loading');
                uploadBtn.disabled = false;
            }
        });
    }
    
    resetForm(type) {
        let form, listId, selectedFilesContainer, uploadBtn, modal;
        
        switch (type) {
            case 'payslip':
                form = document.getElementById('payslip-upload-form');
                listId = 'payslip-file-list';
                selectedFilesContainer = document.getElementById('selectedPayslipFiles');
                uploadBtn = document.getElementById('payslip-upload-btn');
                modal = document.getElementById('payslipImportModal');
                break;
            case 'loans':
                form = document.getElementById('loans-upload-form');
                listId = 'loans-file-list';
                selectedFilesContainer = document.getElementById('selectedLoanFiles');
                uploadBtn = document.getElementById('loans-upload-btn');
                modal = document.getElementById('loansImportModal');
                break;
            case 'allowances':
                form = document.getElementById('allowances-upload-form');
                listId = 'allowances-file-list';
                selectedFilesContainer = document.getElementById('selectedAllowanceFiles');
                uploadBtn = document.getElementById('allowances-upload-btn');
                modal = document.getElementById('allowancesImportModal');
                break;
            default:
                return;
        }
        
        if (form) {
            form.reset();
        }
        
        this.selectedFiles = [];
        this.updateFileList(listId, selectedFilesContainer, uploadBtn);
        
        // Close modal
        if (modal) {
            modal.classList.remove('show');
        }
    }
    
    async loadEmployeesData(page = 1) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.currentPage = page;
        
        const searchTerm = this.getSearchTerm();
        const department = this.getDepartmentFilter();
        
        // Show loading state
        this.showLoadingState(true);
        
        try {
            const activeTab = document.querySelector('.tab-btn.active')?.dataset.tab;
            const params = new URLSearchParams({
                page: page,
                search: searchTerm,
                department: department
            });
            if (activeTab === 'loans-admin') {
                params.append('tab', 'loans');
            }
            
            const response = await fetch(`/finance/employees/?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.employeesData = data.employees;
                this.updateEmployeesTable();
                this.updatePagination(data);
            } else {
                this.showToast('Failed to load employees data', 'error');
            }
        } catch (error) {
            console.error('Error loading employees:', error);
            this.showToast('Error loading employees data', 'error');
        } finally {
            this.isLoading = false;
            this.showLoadingState(false);
        }
    }
    
    getSearchTerm() {
        const activeTab = document.querySelector('.tab-btn.active')?.dataset.tab;
        let searchInput;
        
        if (activeTab === 'loans-admin') {
            searchInput = document.getElementById('loan-employee-search');
        } else if (activeTab === 'allowances-admin') {
            searchInput = document.getElementById('allowance-employee-search');
        }
        
        return searchInput ? searchInput.value.trim() : '';
    }
    
    getDepartmentFilter() {
        const activeTab = document.querySelector('.tab-btn.active')?.dataset.tab;
        let departmentSelect;
        
        if (activeTab === 'loans-admin') {
            departmentSelect = document.getElementById('loan-department-filter');
        } else if (activeTab === 'allowances-admin') {
            departmentSelect = document.getElementById('allowance-department-filter');
        }
        
        return departmentSelect ? departmentSelect.value : '';
    }
    
    showLoadingState(show) {
        const activeTab = document.querySelector('.tab-btn.active')?.dataset.tab;
        let loadingElement;
        
        if (activeTab === 'loans-admin') {
            loadingElement = document.getElementById('loans-loading');
        } else if (activeTab === 'allowances-admin') {
            loadingElement = document.getElementById('allowances-loading');
        }
        
        if (loadingElement) {
            loadingElement.style.display = show ? 'block' : 'none';
        }
    }
    
    updateEmployeesTable() {
        const activeTab = document.querySelector('.tab-btn.active')?.dataset.tab;
        let tbody;
        
        if (activeTab === 'loans-admin') {
            tbody = document.getElementById('loans-employees-tbody');
        } else if (activeTab === 'allowances-admin') {
            tbody = document.getElementById('allowances-employees-tbody');
        }
        
        if (!tbody) return;
        
        tbody.innerHTML = '';
        
        this.employeesData.forEach(employee => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${employee.idnumber || '-'}</td>
                <td>${employee.name}</td>
                <td>${employee.department}</td>
                <td>${employee.employment_type}</td>
                <td>
                    <button class="btn btn-primary btn-sm view-employee-btn" 
                            data-employee-id="${employee.id}" 
                            data-type="${this.currentTab}">
                        <i class="fas fa-eye"></i>
                        View
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }
    
    updatePagination(data) {
        const activeTab = document.querySelector('.tab-btn.active')?.dataset.tab;
        let paginationContainer;
        
        if (activeTab === 'loans-admin') {
            paginationContainer = document.getElementById('loans-pagination');
        } else if (activeTab === 'allowances-admin') {
            paginationContainer = document.getElementById('allowances-pagination');
        }
        
        if (!paginationContainer) return;
        
        paginationContainer.innerHTML = '';
        
        // Previous button
        const prevBtn = document.createElement('button');
        prevBtn.className = `pagination-btn ${!data.has_previous ? 'disabled' : ''}`;
        prevBtn.disabled = !data.has_previous;
        prevBtn.dataset.page = data.page_number - 1;
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        paginationContainer.appendChild(prevBtn);
        
        // Page numbers
        const startPage = Math.max(1, data.page_number - 2);
        const endPage = Math.min(data.total_pages, data.page_number + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `pagination-btn ${i === data.page_number ? 'active' : ''}`;
            pageBtn.dataset.page = i;
            pageBtn.textContent = i;
            paginationContainer.appendChild(pageBtn);
        }
        
        // Next button
        const nextBtn = document.createElement('button');
        nextBtn.className = `pagination-btn ${!data.has_next ? 'disabled' : ''}`;
        nextBtn.disabled = !data.has_next;
        nextBtn.dataset.page = data.page_number + 1;
        nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        paginationContainer.appendChild(nextBtn);
        
        // Page info
        const pageInfo = document.createElement('span');
        pageInfo.className = 'pagination-info';
        pageInfo.textContent = `Page ${data.page_number} of ${data.total_pages} (${data.total_count} total)`;
        paginationContainer.appendChild(pageInfo);
    }
    
    async showEmployeeDetails(employeeId, dataType) {
        const offcanvas = document.getElementById('employeeDetailsOffcanvas');
        const title = document.getElementById('employee-details-title');
        const content = document.getElementById('employee-details-content');
        
        if (!offcanvas || !title || !content) return;
        
        title.textContent = `Employee ${dataType.charAt(0).toUpperCase() + dataType.slice(1)}`;
        content.innerHTML = '<div class="loading-state"><div class="loading-spinner"></div><p>Loading employee details...</p></div>';
        
        this.openOffcanvas('employeeDetailsOffcanvas');
        
        try {
            const endpoint = dataType === 'loans' ? 'loans' : 'allowances';
            const response = await fetch(`/finance/employee/${employeeId}/${endpoint}/`);
            const data = await response.json();
            
            if (data.success) {
                this.renderEmployeeDetails(data, dataType);
            } else {
                content.innerHTML = `<div class="empty-state"><p>${data.message || 'Failed to load data'}</p></div>`;
            }
        } catch (error) {
            console.error('Error loading employee details:', error);
            content.innerHTML = '<div class="empty-state"><p>Error loading data</p></div>';
        }
    }
    
    renderEmployeeDetails(data, dataType) {
        const content = document.getElementById('employee-details-content');
        if (!content) return;
        
        let html = `
            <div class="employee-info">
                <h4>${data.employee.name}</h4>
                <p>ID: ${data.employee.idnumber}</p>
            </div>
        `;
        
        const groups = dataType === 'loans' ? data.loan_groups : data.allowance_groups;
        
        if (Object.keys(groups).length === 0) {
            html += `<div class="empty-state"><p>No ${dataType} found for this employee.</p></div>`;
        } else {
            Object.entries(groups).forEach(([groupName, records]) => {
                html += `
                    <div class="records-section">
                        <h5>${groupName}</h5>
                `;
                
                records.forEach(record => {
                    if (dataType === 'loans') {
                        html += `
                            <div class="record-item">
                                <div class="record-header">
                                    <span class="record-amount">₱${parseFloat(record.amount).toLocaleString()}</span>
                                    <span class="status-badge ${record.status}">${record.status}</span>
                                </div>
                                <div class="record-details">
                                    <div class="record-detail">
                                        <span class="label">Balance:</span>
                                        <span class="value">₱${parseFloat(record.balance).toLocaleString()}</span>
                                    </div>
                                    <div class="record-detail">
                                        <span class="label">Monthly:</span>
                                        <span class="value">₱${parseFloat(record.monthly_deduction).toLocaleString()}</span>
                                    </div>
                                    <div class="record-detail">
                                        <span class="label">Date Issued:</span>
                                        <span class="value">${new Date(record.date_issued).toLocaleDateString()}</span>
                                    </div>
                                    <div class="record-detail">
                                        <span class="label">Terms:</span>
                                        <span class="value">${record.terms_months} months</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    } else {
                        html += `
                            <div class="record-item">
                                <div class="record-header">
                                    <span class="record-amount">₱${parseFloat(record.amount).toLocaleString()}</span>
                                    <span class="status-badge ${record.is_active ? 'active' : 'inactive'}">
                                        ${record.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                                <div class="record-details">
                                    <div class="record-detail">
                                        <span class="label">Frequency:</span>
                                        <span class="value">${record.frequency}</span>
                                    </div>
                                    <div class="record-detail">
                                        <span class="label">Start Date:</span>
                                        <span class="value">${new Date(record.start_date).toLocaleDateString()}</span>
                                    </div>
                                    <div class="record-detail">
                                        <span class="label">End Date:</span>
                                        <span class="value">${record.end_date ? new Date(record.end_date).toLocaleDateString() : 'Ongoing'}</span>
                                    </div>
                                    ${record.description ? `
                                    <div class="record-detail" style="grid-column: 1 / -1;">
                                        <span class="label">Description:</span>
                                        <span class="value">${record.description}</span>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                        `;
                    }
                });
                
                html += '</div>';
            });
        }
        
        content.innerHTML = html;
    }
    
    openOffcanvas(offcanvasId) {
        const offcanvas = document.getElementById(offcanvasId);
        if (offcanvas) {
            offcanvas.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }
    
    closeOffcanvas(offcanvasId) {
        const offcanvas = document.getElementById(offcanvasId);
        if (offcanvas) {
            offcanvas.classList.remove('show');
            document.body.style.overflow = '';
        }
    }
    
    showToast(message, type = 'info') {
        // Use the existing toast notification system from the project
        if (window.portalUI && window.portalUI.showNotification) {
            window.portalUI.showNotification(message, type);
        } else {
            // Fallback if toast system is not available
            console.log(`${type.toUpperCase()}: ${message}`);
            alert(message);
        }
    }
    
    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    initChart() {
        const ctx = document.getElementById('financeChart');
        if (!ctx) {
            console.log('Canvas element not found, skipping chart initialization');
            return;
        }

        // Check if the canvas is properly connected to the DOM
        if (!ctx.ownerDocument || !ctx.ownerDocument.body.contains(ctx)) {
            console.log('Canvas not properly connected to DOM, skipping chart initialization');
            return;
        }

        // Check if the chart container is visible
        const chartContainer = ctx.closest('.chart-container');
        if (chartContainer && chartContainer.offsetParent === null) {
            console.log('Chart container not visible, skipping chart initialization');
            return;
        }

        try {
            // Destroy existing chart if it exists
            if (this.chart) {
                this.chart.destroy();
                this.chart = null;
            }

            // Also destroy any existing Chart.js instances on this canvas
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }

            this.chart = new Chart(ctx, {
                type: this.currentChartType,
                data: {
                    labels: [],
                    datasets: []
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        title: {
                            display: true,
                            text: 'Finance Overview'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            this.loadChartData();
        } catch (error) {
            console.error('Error initializing chart:', error);
        }
    }

    async loadChartData() {
        try {
            // Check if chart exists and is valid
            if (!this.chart) {
                console.log('Chart not initialized, skipping data load');
                return;
            }

            // Ensure we have a default category
            if (!this.currentFilterCategory) {
                this.currentFilterCategory = 'loans';
            }

            const params = new URLSearchParams({
                category: this.currentFilterCategory,
                type: this.currentFilterType,
                period: this.currentChartPeriod
            });

            console.log('Loading chart data with params:', params.toString());

            const response = await fetch(`/finance/chart-data/?${params}`);
            const data = await response.json();

            if (data.success && this.chart) {
                this.chart.data.labels = data.chart_data.labels;
                this.chart.data.datasets = data.chart_data.datasets;
                
                // Update chart title based on current filter
                let title = 'Finance Overview';
                if (this.currentFilterCategory === 'loans') {
                    title = this.currentFilterType ? `${this.currentFilterType} Loans` : 'All Loans';
                } else if (this.currentFilterCategory === 'allowances') {
                    title = this.currentFilterType ? `${this.currentFilterType} Allowances` : 'All Allowances';
                }
                this.chart.options.plugins.title.text = title;
                
                this.chart.update();
                console.log('Chart data loaded successfully');
            } else {
                console.log('Chart data response:', data);
            }
        } catch (error) {
            console.error('Error loading chart data:', error);
        }
    }

    updateChartPeriod(period) {
        this.currentChartPeriod = period;
        
        const chartFilters = document.querySelector('.chart-filters');
        const filterBtns = document.querySelectorAll('.chart-filters .filter-btn');
        
        // Update active button
        filterBtns.forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-period="${period}"]`).classList.add('active');
        
        // Update slider effect
        if (chartFilters) {
            chartFilters.classList.add('has-active');
            const activeIndex = Array.from(filterBtns).findIndex(btn => btn.getAttribute('data-period') === period);
            chartFilters.className = chartFilters.className.replace(/slide-\d/g, '');
            chartFilters.classList.add(`slide-${activeIndex}`);
        }
        
        this.loadChartData();
    }

    switchChartType(type) {
        this.currentChartType = type;
        
        const chartTypeFilters = document.querySelector('.chart-type-filters');
        const chartTypeBtns = document.querySelectorAll('.chart-type-filters .chart-type-btn');
        
        // Update active button
        chartTypeBtns.forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-type="${type}"]`).classList.add('active');
        
        // Update slider effect
        if (chartTypeFilters) {
            chartTypeFilters.classList.add('has-active');
            const activeIndex = Array.from(chartTypeBtns).findIndex(btn => btn.getAttribute('data-type') === type);
            chartTypeFilters.className = chartTypeFilters.className.replace(/slide-\d/g, '');
            chartTypeFilters.classList.add(`slide-${activeIndex}`);
        }
        
        // Reinitialize chart with new type
        this.initChart();
    }

    destroyChart() {
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }
        
        // Also destroy any existing Chart.js instances on the canvas
        const ctx = document.getElementById('financeChart');
        if (ctx) {
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }
        }
    }


}



document.addEventListener('DOMContentLoaded', function () {
    const tabList = document.querySelector('.tab-list');
    if (tabList) {
        tabList.addEventListener('click', function (e) {
            const tab = e.target.closest('.tab');
            if (!tab) return;
            e.preventDefault();
            e.stopPropagation();
            tabList.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));
            tab.classList.add('active');
            const targetId = tab.getAttribute('data-target');
            if (targetId) {
                const content = document.getElementById(targetId);
                if (content) content.classList.add('active');
            }
            const componentCard = document.querySelector('.component-card');
            if (componentCard) {
                componentCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        });
    }
});

document.addEventListener('DOMContentLoaded', function () {
    // Payslip import modal
    const closePayslipBtn = document.getElementById('closePayslipImportModalBtn');
    const payslipModal = document.getElementById('payslipImportModal');
    if (closePayslipBtn && payslipModal) {
        function closePayslipModalWithAnimation() {
            payslipModal.classList.add('closing');
            setTimeout(() => {
                payslipModal.classList.remove('show', 'closing');
            }, 200);
        }
        closePayslipBtn.addEventListener('click', closePayslipModalWithAnimation);
        payslipModal.querySelector('.modal-overlay').addEventListener('click', closePayslipModalWithAnimation);
        const cancelPayslipBtn = document.getElementById('cancelPayslipImportBtn');
        if (cancelPayslipBtn) {
            cancelPayslipBtn.addEventListener('click', closePayslipModalWithAnimation);
        }
    }
    
    // Loans import modal
    const closeLoansBtn = document.getElementById('closeLoansImportModalBtn');
    const loansModal = document.getElementById('loansImportModal');
    if (closeLoansBtn && loansModal) {
        function closeLoansModalWithAnimation() {
            loansModal.classList.add('closing');
            setTimeout(() => {
                loansModal.classList.remove('show', 'closing');
            }, 200);
        }
        closeLoansBtn.addEventListener('click', closeLoansModalWithAnimation);
        loansModal.querySelector('.modal-overlay').addEventListener('click', closeLoansModalWithAnimation);
        const cancelLoansBtn = document.getElementById('cancelLoanImportBtn');
        if (cancelLoansBtn) {
            cancelLoansBtn.addEventListener('click', closeLoansModalWithAnimation);
        }
    }
    
    // Allowances import modal
    const closeAllowancesBtn = document.getElementById('closeAllowancesImportModalBtn');
    const allowancesModal = document.getElementById('allowancesImportModal');
    if (closeAllowancesBtn && allowancesModal) {
        function closeAllowancesModalWithAnimation() {
            allowancesModal.classList.add('closing');
            setTimeout(() => {
                allowancesModal.classList.remove('show', 'closing');
            }, 200);
        }
        closeAllowancesBtn.addEventListener('click', closeAllowancesModalWithAnimation);
        allowancesModal.querySelector('.modal-overlay').addEventListener('click', closeAllowancesModalWithAnimation);
        const cancelAllowancesBtn = document.getElementById('cancelAllowanceImportBtn');
        if (cancelAllowancesBtn) {
            cancelAllowancesBtn.addEventListener('click', closeAllowancesModalWithAnimation);
        }
    }
    // Payslip upload button state will be handled by the new system
    // OJT Payslip Template Download
    const ojtTemplateBtn = document.getElementById('ojtPayslipTemplateBtn');
    if (ojtTemplateBtn) {
        ojtTemplateBtn.addEventListener('click', function(e) {
            e.preventDefault();
            window.open('/finance/template/ojt_payslip/', '_blank');
        });
    }
});

document.addEventListener('DOMContentLoaded', function () {
    const searchInput = document.getElementById('searchInput');
    const tableContainer = document.getElementById('employeeTableContainer');
    let searchTimeout = null;
    function fetchTable(url) {
        fetch(url, { headers: { 'X-Requested-With': 'XMLHttpRequest' } })
            .then(response => response.json())
            .then(data => {
                tableContainer.innerHTML = data.html;
                // No need to re-attach handlers, event delegation handles it
            });
    }
    if (tableContainer) {
        tableContainer.addEventListener('click', function(e) {
            const a = e.target.closest('a');
            if (a && a.classList.contains('pagination-btn')) {
                e.preventDefault();
                fetchTable(a.href.replace('/?', '/finance/employee-table/?'));
            }
            // All other anchor tags (like View) will navigate normally
        });
    }
    if (searchInput) {
        searchInput.addEventListener('input', function () {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const search = encodeURIComponent(searchInput.value);
                fetchTable(`/finance/employee-table/?search=${search}`);
            }, 300);
        });
    }
});



function attachChartButtonHandlers() {
    document.querySelectorAll('.filter-btn[data-period]').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            if (window.adminFinance) {
                window.adminFinance.updateChartPeriod(this.getAttribute('data-period'));
            }
        });
    });
    document.querySelectorAll('.chart-type-btn[data-type]').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            if (window.adminFinance) {
                window.adminFinance.switchChartType(this.getAttribute('data-type'));
            }
        });
    });
    
    // Initialize slider effect
    const chartFilters = document.querySelector('.chart-filters');
    const chartTypeFilters = document.querySelector('.chart-type-filters');
    
    if (chartFilters) {
        const activeFilterBtn = chartFilters.querySelector('.filter-btn.active');
        if (activeFilterBtn) {
            const activeIndex = Array.from(chartFilters.querySelectorAll('.filter-btn')).indexOf(activeFilterBtn);
            chartFilters.classList.add('has-active', `slide-${activeIndex}`);
        }
    }
    
    if (chartTypeFilters) {
        const activeChartTypeBtn = chartTypeFilters.querySelector('.chart-type-btn.active');
        if (activeChartTypeBtn) {
            const activeIndex = Array.from(chartTypeFilters.querySelectorAll('.chart-type-btn')).indexOf(activeChartTypeBtn);
            chartTypeFilters.classList.add('has-active', `slide-${activeIndex}`);
        }
    }
}

document.addEventListener('DOMContentLoaded', function () {
    window.adminFinance = new AdminFinanceModule();
    attachChartButtonHandlers();

    // Initialize new payslip upload system
    initializePayslipUploadSystem();
});

// New Comprehensive Payslip Upload System
function initializePayslipUploadSystem() {
    // Initialize upload button state management
    initializePayslipUploadButton();

    // Override the existing payslip form handler
    const payslipUploadForm = document.getElementById('payslip-upload-form');
    if (payslipUploadForm) {
        payslipUploadForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const tabRegular = document.getElementById('tab-regular-probationary');
            const tabOJT = document.getElementById('tab-ojt');

            if (tabRegular && tabRegular.classList.contains('active')) {
                handleRegularPayslipUpload();
            } else if (tabOJT && tabOJT.classList.contains('active')) {
                handleOJTPayslipUpload();
            }
        });
    }

    // Progress modal event handlers
    const closeProgressBtn = document.getElementById('closePayslipProgressBtn');
    const progressModal = document.getElementById('payslipProgressModal');

    if (closeProgressBtn && progressModal) {
        closeProgressBtn.addEventListener('click', () => {
            progressModal.classList.remove('show');
            setTimeout(() => window.location.reload(), 500);
        });
    }

    // Results modal event handlers
    const viewResultsBtn = document.getElementById('viewPayslipResultsBtn');
    const resultsModal = document.getElementById('payslipResultsModal');
    const closeResultsBtn = document.getElementById('closePayslipResultsModal');
    const closeResultsModalBtn = document.getElementById('closeResultsModalBtn');

    if (viewResultsBtn) {
        viewResultsBtn.addEventListener('click', () => {
            showPayslipResultsModal();
        });
    }

    if (closeResultsBtn && resultsModal) {
        closeResultsBtn.addEventListener('click', () => {
            resultsModal.classList.remove('show');
        });
    }

    if (closeResultsModalBtn && resultsModal) {
        closeResultsModalBtn.addEventListener('click', () => {
            resultsModal.classList.remove('show');
        });
    }

    // Results modal tab switching
    const resultsTabs = document.querySelectorAll('.results-tabs .tab');
    const resultsTabPanels = document.querySelectorAll('.results-tabs .tab-panel');

    resultsTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const targetTab = tab.dataset.tab;

            resultsTabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');

            resultsTabPanels.forEach(panel => {
                panel.classList.remove('active');
                if (panel.id === `tab-${targetTab}`) {
                    panel.classList.add('active');
                }
            });
        });
    });
}

function initializePayslipUploadButton() {
    const payslipUploadBtn = document.getElementById('payslip-upload-btn');
    const fileInputRegular = document.getElementById('payslip-files-regular');
    const fileInputOJT = document.getElementById('payslip-files-ojt');
    const tabRegular = document.getElementById('tab-regular-probationary');
    const tabOJT = document.getElementById('tab-ojt');
    const cancelBtn = document.getElementById('cancelPayslipImportBtn');

    function updatePayslipUploadBtnState() {
        const payslipUploadBtn = document.getElementById('payslip-upload-btn');
        if (!payslipUploadBtn) return;

        // Get current tab elements
        const currentTabRegular = document.getElementById('tab-regular-probationary');
        const currentTabOJT = document.getElementById('tab-ojt');
        const currentFileInputRegular = document.getElementById('payslip-files-regular');
        const currentFileInputOJT = document.getElementById('payslip-files-ojt');

        let hasFiles = false;
        let hasRequiredFields = true;

        console.log('Checking button state. Regular active:', currentTabRegular?.classList.contains('active'), 'OJT active:', currentTabOJT?.classList.contains('active'));

        if (currentTabRegular && currentTabRegular.classList.contains('active')) {
            // For Regular tab: need dates AND files
            const startDate = document.getElementById('cutoff-start-date');
            const endDate = document.getElementById('cutoff-end-date');
            hasFiles = currentFileInputRegular && currentFileInputRegular.files && currentFileInputRegular.files.length > 0;
            hasRequiredFields = !!(startDate && startDate.value && endDate && endDate.value);
            console.log('Regular tab - hasFiles:', hasFiles, 'hasRequiredFields:', hasRequiredFields, 'files count:', currentFileInputRegular?.files?.length, 'start date:', startDate?.value, 'end date:', endDate?.value);
        } else if (currentTabOJT && currentTabOJT.classList.contains('active')) {
            // For OJT tab: only need files
            hasFiles = currentFileInputOJT && currentFileInputOJT.files && currentFileInputOJT.files.length > 0;
            hasRequiredFields = true; // No additional fields required for OJT
            console.log('OJT tab - hasFiles:', hasFiles, 'files count:', currentFileInputOJT?.files?.length);
        }

        const shouldEnable = hasFiles && hasRequiredFields;
        console.log('Button should be enabled:', shouldEnable);
        payslipUploadBtn.disabled = !shouldEnable;
    }

    // Expose function globally
    window.updatePayslipUploadBtnState = updatePayslipUploadBtnState;

    if (fileInputRegular) {
        fileInputRegular.addEventListener('change', updatePayslipUploadBtnState);
    }
    if (fileInputOJT) {
        fileInputOJT.addEventListener('change', updatePayslipUploadBtnState);
    }

    // Add event listeners for date fields
    const startDateInput = document.getElementById('cutoff-start-date');
    const endDateInput = document.getElementById('cutoff-end-date');
    if (startDateInput) {
        startDateInput.addEventListener('change', updatePayslipUploadBtnState);
    }
    if (endDateInput) {
        endDateInput.addEventListener('change', updatePayslipUploadBtnState);
    }

    const payslipTabs = document.getElementById('payslipImportTabs');
    if (payslipTabs) {
        const tabBtns = payslipTabs.querySelectorAll('.tab-list .tab');
        tabBtns.forEach((btn, idx) => {
            btn.addEventListener('click', function() {
                tabBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                if (tabRegular) tabRegular.classList.remove('active');
                if (tabOJT) tabOJT.classList.remove('active');

                const tabData = btn.dataset.tab;
                if (tabData === 'regular-probationary' && tabRegular) {
                    tabRegular.classList.add('active');
                } else if (tabData === 'ojt' && tabOJT) {
                    tabOJT.classList.add('active');
                }
                setTimeout(updatePayslipUploadBtnState, 50);
            });
        });
        if (tabBtns.length > 0) {
            tabBtns[0].classList.add('active');
            if (tabRegular) tabRegular.classList.add('active');
        }
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            if (fileInputRegular) fileInputRegular.value = '';
            if (fileInputOJT) fileInputOJT.value = '';
            updatePayslipUploadBtnState();
        });
    }

    setTimeout(updatePayslipUploadBtnState, 100);
    window.updatePayslipUploadBtnState = updatePayslipUploadBtnState;
}

function handleRegularPayslipUpload() {
    console.log('Starting Regular/Probationary payslip upload (PDF files → Payslip model)');
    const cutoffStartDate = document.getElementById('cutoff-start-date');
    const cutoffEndDate = document.getElementById('cutoff-end-date');
    const fileInputRegular = document.getElementById('payslip-files-regular');

    // Validate dates and PDF files
    if (!validateRegularPayslipInputs(cutoffStartDate, cutoffEndDate, fileInputRegular)) {
        return;
    }

    console.log('Validation passed. Files:', fileInputRegular.files.length, 'Start date:', cutoffStartDate.value, 'End date:', cutoffEndDate.value);

    // Close the import modal immediately
    const uploadModal = document.getElementById('payslipImportModal');
    if (uploadModal) {
        console.log('Closing upload modal');
        uploadModal.classList.remove('show');
    } else {
        console.log('Upload modal not found');
    }

    // Create and show persistent progress toast
    const toastId = 'payslip-upload-progress-toast';
    console.log('Creating progress toast with ID:', toastId);
    createPersistentProgressToast(toastId, 'Uploading payslips: 0%');

    const formData = new FormData();
    formData.append('cutoff_from', cutoffStartDate.value);
    formData.append('cutoff_to', cutoffEndDate.value);
    for (let i = 0; i < fileInputRegular.files.length; i++) {
        formData.append('payslip_files', fileInputRegular.files[i]);
    }

    console.log('Sending Regular payslip upload request to /finance/payslips/upload/');
    fetch('/finance/payslips/upload/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => {
        console.log('Regular upload response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Regular upload response data:', data);
        if (data.success) {
            startPayslipProgressPolling(data.upload_id, toastId);
        } else {
            updateProgressToastError(toastId, data.message || 'Upload failed');
        }
        if (fileInputRegular) fileInputRegular.value = '';
        if (window.updatePayslipUploadBtnState) window.updatePayslipUploadBtnState();
    })
    .catch(error => {
        console.error('Regular upload error:', error);
        updateProgressToastError(toastId, 'Upload failed: ' + error.message);
        if (fileInputRegular) fileInputRegular.value = '';
        if (window.updatePayslipUploadBtnState) window.updatePayslipUploadBtnState();
    });
}

// Validation for Regular/Probationary payslips (PDF files)
function validateRegularPayslipInputs(cutoffStartDate, cutoffEndDate, fileInput) {
    let valid = true;

    const cutoffStartError = document.getElementById('cutoff-start-date-error');
    const cutoffEndError = document.getElementById('cutoff-end-date-error');

    if (cutoffStartError) cutoffStartError.style.display = 'none';
    if (cutoffEndError) cutoffEndError.style.display = 'none';

    // Validate start date
    if (!cutoffStartDate || !cutoffStartDate.value) {
        if (cutoffStartError) {
            cutoffStartError.querySelector('.error-text').textContent = 'Start cut off date is required';
            cutoffStartError.style.display = 'block';
        }
        valid = false;
    }

    // Validate end date
    if (!cutoffEndDate || !cutoffEndDate.value) {
        if (cutoffEndError) {
            cutoffEndError.querySelector('.error-text').textContent = 'End cut off date is required';
            cutoffEndError.style.display = 'block';
        }
        valid = false;
    }

    // Validate date range
    if (cutoffStartDate && cutoffEndDate && cutoffStartDate.value && cutoffEndDate.value) {
        const startDate = new Date(cutoffStartDate.value);
        const endDate = new Date(cutoffEndDate.value);

        if (startDate >= endDate) {
            if (cutoffEndError) {
                cutoffEndError.querySelector('.error-text').textContent = 'End date must be after start date';
                cutoffEndError.style.display = 'block';
            }
            valid = false;
        }
    }

    // Validate PDF files
    if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
        const toastId = 'payslip-validation-error-toast';
        createPersistentProgressToast(toastId, 'Please select at least one PDF file');
        updateProgressToastError(toastId, 'Please select at least one PDF file');
        valid = false;
    } else {
        // Check file types
        let invalidFiles = [];
        for (let i = 0; i < fileInput.files.length; i++) {
            const file = fileInput.files[i];
            if (!file.name.toLowerCase().endsWith('.pdf')) {
                invalidFiles.push(file.name);
            }
        }

        if (invalidFiles.length > 0) {
            const toastId = 'payslip-validation-error-toast';
            createPersistentProgressToast(toastId, 'Only PDF files are allowed');
            updateProgressToastError(toastId, 'Only PDF files are allowed');
            valid = false;
        }
    }

    return valid;
}

// Validation for OJT payslips (Excel/CSV files)
function validateOJTPayslipInputs(fileInput) {
    let valid = true;

    // Validate Excel/CSV files
    if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
        const toastId = 'payslip-validation-error-toast';
        createPersistentProgressToast(toastId, 'Please select at least one Excel/CSV file');
        updateProgressToastError(toastId, 'Please select at least one Excel/CSV file');
        valid = false;
    } else {
        // Check file types
        let invalidFiles = [];
        for (let i = 0; i < fileInput.files.length; i++) {
            const file = fileInput.files[i];
            const fileName = file.name.toLowerCase();
            if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls') && !fileName.endsWith('.csv')) {
                invalidFiles.push(file.name);
            }
        }

        if (invalidFiles.length > 0) {
            const toastId = 'payslip-validation-error-toast';
            createPersistentProgressToast(toastId, 'Only Excel/CSV files are allowed');
            updateProgressToastError(toastId, 'Only Excel/CSV files are allowed');
            valid = false;
        }
    }

    return valid;
}

function startPayslipProgressPolling(uploadId, toastId) {
    const pollInterval = setInterval(() => {
        fetch(`/finance/payslips/upload-progress/${uploadId}/`)
            .then(response => response.json())
            .then(data => {
                // Update progress in both the modal (for backward compatibility) and toast
                updatePayslipProgress(data);
                updateProgressToastProgress(toastId, data);

                if (data.status === 'completed' || data.status === 'failed') {
                    clearInterval(pollInterval);
                    handlePayslipUploadCompletion(data, toastId);
                }
            })
            .catch(error => {
                console.error('Progress polling error:', error);
                clearInterval(pollInterval);
                updateProgressToastError(toastId, 'Failed to get upload progress');
            });
    }, 1000);
}

function updatePayslipProgress(data) {
    const progressFill = document.getElementById('payslipProgressFill');
    const progressText = document.getElementById('payslipProgressText');
    const progressCount = document.getElementById('payslipProgressCount');
    const successCount = document.getElementById('payslipSuccessCount');
    const errorCount = document.getElementById('payslipErrorCount');
    const importDetails = document.getElementById('payslipImportDetails');

    let percentage = 0;
    if (data.total && !isNaN(data.total) && data.total > 0) {
        percentage = Math.round((data.processed / data.total) * 100);
    }

    if (progressFill) progressFill.style.width = percentage + '%';
    if (progressText) progressText.textContent = percentage + '%';
    if (progressCount) progressCount.textContent = `${data.processed} of ${data.total} files`;

    if (data.processed > 0 && importDetails) {
        importDetails.style.display = 'block';
        if (successCount) successCount.textContent = data.success_count;
        if (errorCount) errorCount.textContent = data.error_count;
    }
}

function handlePayslipUploadCompletion(data, toastId) {
    const progressModal = document.getElementById('payslipProgressModal');
    const resultsModal = document.getElementById('payslipResultsModal');

    if (data.status === 'completed') {
        if (data.error_count > 0) {
            // Show error toast with download button
            window.payslipUploadResults = data;
            updateProgressToastError(toastId, 'Upload completed with errors', data);

            // Keep modal functionality for backward compatibility
            if (progressModal) progressModal.classList.remove('show');
            if (resultsModal) resultsModal.classList.add('show');
        } else {
            // Show success toast and auto-close
            updateProgressToastSuccess(toastId, 'Payslips uploaded successfully!');

            // Keep modal functionality for backward compatibility
            if (progressModal) progressModal.classList.remove('show');
            if (resultsModal) resultsModal.classList.remove('show');

            setTimeout(() => { window.location.reload(); }, 3000);
        }
    } else if (data.status === 'failed') {
        updateProgressToastError(toastId, data.error_message || 'Upload failed');
    }
}

function showPayslipError(message) {
    const progressModal = document.getElementById('payslipProgressModal');
    if (progressModal) progressModal.classList.remove('show');

    if (window.adminFinance && window.adminFinance.showToast) {
        window.adminFinance.showToast(message, 'error');
    } else {
        alert(message);
    }
}

// Create a persistent toast notification for progress tracking
function createPersistentProgressToast(toastId, message) {
    console.log('createPersistentProgressToast called with ID:', toastId, 'message:', message);

    // Remove any existing toast with the same ID
    const existingToast = document.getElementById(toastId);
    if (existingToast) {
        console.log('Removing existing toast');
        existingToast.remove();
    }

    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        console.log('Creating new toast container');
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    } else {
        console.log('Using existing toast container');
    }

    // Create the toast
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = 'toast toast-info persistent-toast';
    toast.style.display = 'flex';
    toast.style.alignItems = 'center';
    toast.style.gap = '12px';
    toast.style.padding = '12px 16px';
    toast.style.position = 'relative';

    // Add spinner and message
    toast.innerHTML = `
        <div class="spinner-border spinner-border-sm text-light" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <span class="progress-message">${message}</span>
    `;

    console.log('Appending toast to container');
    toastContainer.appendChild(toast);
    console.log('Toast created and appended');
    return toast;
}

// Update progress percentage in toast
function updateProgressToastProgress(toastId, data) {
    const toast = document.getElementById(toastId);
    if (!toast) return;

    let percentage = 0;
    if (data.total && !isNaN(data.total) && data.total > 0) {
        percentage = Math.round((data.processed / data.total) * 100);
    }

    const messageEl = toast.querySelector('.progress-message');
    if (messageEl) {
        messageEl.textContent = `Uploading payslips: ${percentage}%`;
    }
}

// Update toast to show success and auto-close
function updateProgressToastSuccess(toastId, message) {
    const toast = document.getElementById(toastId);
    if (!toast) return;

    // Remove spinner and update styling
    toast.className = 'toast toast-success persistent-toast';
    toast.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span class="progress-message">${message}</span>
    `;

    // Auto-close after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.style.opacity = '0';
            toast.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                if (toast.parentNode) toast.remove();
            }, 300);
        }
    }, 3000);
}

// Update toast to show error with optional download button
function updateProgressToastError(toastId, message, errorData = null) {
    const toast = document.getElementById(toastId);
    if (!toast) return;

    // Remove spinner and update styling
    toast.className = 'toast toast-error persistent-toast';

    let downloadButton = '';
    if (errorData && errorData.errors && errorData.errors.length > 0) {
        downloadButton = `
            <button class="btn btn-sm btn-outline-light ms-2" onclick="downloadFailedPayslipsServer(window.payslipUploadResults)">
                <i class="fas fa-download"></i> Download Failed Items
            </button>
        `;
    }

    toast.innerHTML = `
        <i class="fas fa-exclamation-circle"></i>
        <span class="progress-message">${message}</span>
        ${downloadButton}
        <button class="message-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Auto-close after 1 minute
    setTimeout(() => {
        if (toast.parentNode) {
            toast.style.opacity = '0';
            toast.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                if (toast.parentNode) toast.remove();
            }, 300);
        }
    }, 60000);
}

// Simulate upload progress for OJT uploads (since they don't have server-side progress tracking)
function simulateUploadProgress(toastId) {
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15; // Random increment between 0-15%
        if (progress > 95) progress = 95; // Cap at 95% until actual completion

        const toast = document.getElementById(toastId);
        if (toast) {
            const messageEl = toast.querySelector('.progress-message');
            if (messageEl) {
                messageEl.textContent = `Uploading payslips: ${Math.round(progress)}%`;
            }
        }
    }, 500);

    return interval;
}

// Download failed payslips using server-side Excel generation
function downloadFailedPayslipsServer(data) {
    if (!data || !data.errors || data.errors.length === 0) {
        console.error('No error data available for download');
        return;
    }

    // Send error data to server for Excel generation
    fetch('/finance/payslips/failed-uploads/download/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            errors: data.errors
        })
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('Failed to generate Excel file');
    })
    .then(blob => {
        // Create download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'payslip_upload_errors.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Show success message
        if (window.adminFinance && window.adminFinance.showToast) {
            window.adminFinance.showToast('Error report downloaded successfully', 'success');
        }
    })
    .catch(error => {
        console.error('Download error:', error);
        if (window.adminFinance && window.adminFinance.showToast) {
            window.adminFinance.showToast('Failed to download error report', 'error');
        }
    });
}

function handleOJTPayslipUpload() {
    console.log('Starting OJT payslip upload (Excel/CSV files → OJTPayslipData model)');
    const fileInputOJT = document.getElementById('payslip-files-ojt');

    // Validate Excel/CSV files
    if (!validateOJTPayslipInputs(fileInputOJT)) {
        return;
    }

    console.log('Validation passed. Files:', fileInputOJT.files.length);

    // Close the import modal immediately
    const uploadModal = document.getElementById('payslipImportModal');
    if (uploadModal) uploadModal.classList.remove('show');

    // Create and show persistent progress toast
    const toastId = 'payslip-upload-progress-toast';
    createPersistentProgressToast(toastId, 'Uploading payslips: 0%');

    const formData = new FormData();
    for (let i = 0; i < fileInputOJT.files.length; i++) {
        formData.append('files', fileInputOJT.files[i]);
    }

    // Simulate progress for OJT uploads (since they don't have server-side progress tracking)
    let progressInterval = simulateUploadProgress(toastId);

    console.log('Sending OJT payslip upload request to /finance/payslip/upload/');
    fetch('/finance/payslip/upload/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => {
        console.log('OJT upload response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('OJT upload response data:', data);
        clearInterval(progressInterval);

        if (data.success) {
            // Update toast to show success and auto-close
            updateProgressToastSuccess(toastId, `Successfully uploaded ${data.created} OJT payslip(s)`);
            setTimeout(() => { window.location.reload(); }, 3000);
        } else {
            let errorMsg = data.errors ? data.errors.join('\n') : (data.error || 'Upload failed');
            updateProgressToastError(toastId, errorMsg);
        }
        if (fileInputOJT) fileInputOJT.value = '';
        if (window.updatePayslipUploadBtnState) window.updatePayslipUploadBtnState();
    })
    .catch(error => {
        console.error('OJT upload error:', error);
        clearInterval(progressInterval);
        updateProgressToastError(toastId, 'Upload failed: ' + error.message);
        if (fileInputOJT) fileInputOJT.value = '';
        if (window.updatePayslipUploadBtnState) window.updatePayslipUploadBtnState();
    });
}

function showPayslipResultsModal() {
    const resultsModal = document.getElementById('payslipResultsModal');
    const progressModal = document.getElementById('payslipProgressModal');

    if (!window.payslipUploadResults) {
        console.error('No upload results available');
        return;
    }

    const data = window.payslipUploadResults;

    const successfulCount = document.getElementById('payslipSuccessfulCount');
    const failedCount = document.getElementById('payslipFailedCount');

    if (successfulCount) successfulCount.textContent = data.success_count || 0;
    if (failedCount) failedCount.textContent = data.error_count || 0;

    const successTable = document.getElementById('successfulUploadsTable');
    if (successTable && data.successful_uploads) {
        successTable.innerHTML = '';
        data.successful_uploads.forEach(upload => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${upload.filename}</td>
                <td>${upload.employee_id}</td>
                <td>${upload.employee_name}</td>
                <td>${upload.final_filename}</td>
                <td><span class="status-badge ${upload.action.toLowerCase()}">${upload.action}</span></td>
            `;
            successTable.appendChild(row);
        });
    }

    const failedTable = document.getElementById('failedUploadsTable');
    if (failedTable && data.errors) {
        failedTable.innerHTML = '';
        data.errors.forEach(error => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${error.filename}</td>
                <td class="error-message">${error.error}</td>
            `;
            failedTable.appendChild(row);
        });
    }

    const downloadErrorBtn = document.getElementById('downloadPayslipErrorReportBtn');
    if (downloadErrorBtn) {
        if (data.error_count > 0) {
            downloadErrorBtn.style.display = 'inline-block';
            downloadErrorBtn.onclick = () => downloadPayslipErrorReportXLSX(data);
        } else {
            downloadErrorBtn.style.display = 'none';
        }
    }

    if (progressModal) progressModal.classList.remove('show');
    if (resultsModal) resultsModal.classList.add('show');
}

// XLSX error report
function downloadPayslipErrorReportXLSX(data) {
    if (!data.errors || data.errors.length === 0) return;
    const ws_data = [['Filename', 'Error']];
    data.errors.forEach(error => {
        ws_data.push([error.filename, error.error]);
    });
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(ws_data);
    XLSX.utils.book_append_sheet(wb, ws, 'Failed Uploads');
    XLSX.writeFile(wb, 'payslip_upload_errors.xlsx');
}

// Import Popover System
document.addEventListener('DOMContentLoaded', function () {
    const importBtn = document.getElementById('importBtn');
    const importPopover = document.getElementById('importPopover');
    const importOptionsPopover = document.getElementById('importOptionsPopover');
    const importOptions = document.querySelectorAll('.import-option');
    const importOptionsGroups = document.querySelectorAll('.import-options-group');

    let currentActiveGroup = null;
    let popoverTimeout = null;

    // Show main import popover
    function showImportPopover() {
        if (popoverTimeout) {
            clearTimeout(popoverTimeout);
            popoverTimeout = null;
        }
        importPopover.classList.add('show');
    }

    // Hide main import popover
    function hideImportPopover() {
        popoverTimeout = setTimeout(() => {
            importPopover.classList.remove('show');
            hideImportOptionsPopover();
        }, 150);
    }

    // Show import options popover
    function showImportOptionsPopover(groupType) {
        if (popoverTimeout) {
            clearTimeout(popoverTimeout);
            popoverTimeout = null;
        }

        // Hide all groups
        importOptionsGroups.forEach(group => {
            group.style.display = 'none';
        });

        // Show the selected group
        const targetGroup = document.querySelector(`[data-group="${groupType}"]`);
        if (targetGroup) {
            targetGroup.style.display = 'block';
            currentActiveGroup = groupType;
        }

        importOptionsPopover.classList.add('show');
    }

    // Hide import options popover
    function hideImportOptionsPopover() {
        popoverTimeout = setTimeout(() => {
            importOptionsPopover.classList.remove('show');
            currentActiveGroup = null;
        }, 150);
    }

    // Import button events
    if (importBtn) {
        importBtn.addEventListener('mouseenter', showImportPopover);
        importBtn.addEventListener('mouseleave', hideImportPopover);
        importBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            if (importPopover.classList.contains('show')) {
                hideImportPopover();
            } else {
                showImportPopover();
            }
        });
    }

    // Main popover events
    if (importPopover) {
        importPopover.addEventListener('mouseenter', showImportPopover);
        importPopover.addEventListener('mouseleave', hideImportPopover);
    }

    // Import option hover events
    importOptions.forEach(option => {
        option.addEventListener('mouseenter', function() {
            const type = this.dataset.type;
            showImportOptionsPopover(type);
        });

        option.addEventListener('mouseleave', function() {
            // Don't hide immediately, let the user move to the options popover
        });
    });

    // Options popover events
    if (importOptionsPopover) {
        importOptionsPopover.addEventListener('mouseenter', function() {
            if (popoverTimeout) {
                clearTimeout(popoverTimeout);
                popoverTimeout = null;
            }
        });

        importOptionsPopover.addEventListener('mouseleave', hideImportOptionsPopover);
    }

    // Handle radio button selection
    const radioButtons = document.querySelectorAll('.import-options-popover input[type="radio"]');
    radioButtons.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                // Hide both popovers
                importPopover.classList.remove('show');
                importOptionsPopover.classList.remove('show');

                // Trigger the import modal based on selection
                const value = this.value;
                if (value.startsWith('payslips_')) {
                    // Set the appropriate payslip type and open modal
                    const subType = value.split('_')[1]; // 'regular' or 'ojt'
                    openImportModal('payslips', subType);
                } else if (value.startsWith('loans_')) {
                    const subType = value.split('_')[1]; // 'principal' or 'deduction'
                    openImportModal('loans', subType);
                } else if (value === 'allowances') {
                    openImportModal('allowances');
                }
            }
        });
    });

    // Close popovers when clicking outside
    document.addEventListener('click', function(e) {
        if (!importBtn.contains(e.target) &&
            !importPopover.contains(e.target) &&
            !importOptionsPopover.contains(e.target)) {
            importPopover.classList.remove('show');
            importOptionsPopover.classList.remove('show');
        }
    });

    // Updated openImportModal function
    window.openImportModal = function(type, subType = null) {
        let modalId;

        switch (type) {
            case 'payslips':
                modalId = 'payslipImportModal';
                // Set the appropriate tab based on subType
                if (subType === 'ojt') {
                    // Switch to OJT tab
                    const ojtTab = document.querySelector('[data-tab="ojt"]');
                    const ojtPanel = document.getElementById('tab-ojt');
                    const regularTab = document.querySelector('[data-tab="regular-probationary"]');
                    const regularPanel = document.getElementById('tab-regular-probationary');

                    if (ojtTab && ojtPanel && regularTab && regularPanel) {
                        regularTab.classList.remove('active');
                        regularPanel.classList.remove('active');
                        ojtTab.classList.add('active');
                        ojtPanel.classList.add('active');
                    }
                } else {
                    // Default to Regular/Probationary tab
                    const regularTab = document.querySelector('[data-tab="regular-probationary"]');
                    const regularPanel = document.getElementById('tab-regular-probationary');
                    const ojtTab = document.querySelector('[data-tab="ojt"]');
                    const ojtPanel = document.getElementById('tab-ojt');

                    if (regularTab && regularPanel && ojtTab && ojtPanel) {
                        ojtTab.classList.remove('active');
                        ojtPanel.classList.remove('active');
                        regularTab.classList.add('active');
                        regularPanel.classList.add('active');
                    }
                }
                break;
            case 'loans':
                modalId = 'loansImportModal';
                break;
            case 'allowances':
                modalId = 'allowancesImportModal';
                break;
            default:
                modalId = 'payslipImportModal';
        }

        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
        }
    };
});