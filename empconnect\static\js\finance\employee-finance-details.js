document.addEventListener('DOMContentLoaded', function() {
    // Initialize the employee finance details page
    initializeEmployeeFinanceDetails();
});

function initializeEmployeeFinanceDetails() {
    // Add any interactive functionality here
    console.log('Employee Finance Details page initialized');
    
    // Add smooth scrolling for better UX
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add hover effects for cards
    document.querySelectorAll('.payslip-card, .loan-group, .allowance-group').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
    
    // Add download functionality for payslip files
    document.querySelectorAll('.payslip-card .btn-outline').forEach(btn => {
        btn.addEventListener('click', function(e) {
            // The download will happen automatically due to the href attribute
            // But we can add some visual feedback
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Downloading...';
            this.disabled = true;
            
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 2000);
        });
    });
} 