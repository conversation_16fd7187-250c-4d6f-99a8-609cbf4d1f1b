document.addEventListener('DOMContentLoaded', function () {
  const toggleGroup = document.querySelector('.import-type-toggle-group');
  if (!toggleGroup) return;

  let openRadio = null;

  toggleGroup.addEventListener('click', function (e) {
    const btn = e.target.closest('.import-type-btn');
    if (!btn) return;
    const type = btn.getAttribute('data-toggle');
    const radioGroup = toggleGroup.querySelector('.import-type-radio-group[data-radio="' + type + '"]');
    if (!radioGroup) return;

    // If already open, close it
    if (radioGroup.classList.contains('active')) {
      radioGroup.classList.remove('active');
      btn.classList.remove('active');
      openRadio = null;
      return;
    }

    // Close any open group
    if (openRadio && openRadio !== radioGroup) {
      openRadio.classList.remove('active');
      const openBtn = toggleGroup.querySelector('.import-type-btn.active');
      if (openBtn) openBtn.classList.remove('active');
    }

    // Open the clicked group
    radioGroup.classList.add('active');
    btn.classList.add('active');
    openRadio = radioGroup;
  });

  // Optional: close all when clicking outside
  document.addEventListener('click', function (e) {
    if (!toggleGroup.contains(e.target)) {
      const open = toggleGroup.querySelector('.import-type-radio-group.active');
      const openBtn = toggleGroup.querySelector('.import-type-btn.active');
      if (open) open.classList.remove('active');
      if (openBtn) openBtn.classList.remove('active');
      openRadio = null;
    }
  });
}); 