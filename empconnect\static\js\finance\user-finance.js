class EmployeeFinanceModule {
    constructor() {
        this.currentPayslip = null;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupTabSwitching();
    }
    
    setupEventListeners() {
        // Send payslip buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.send-payslip-btn')) {
                const btn = e.target.closest('.send-payslip-btn');
                const payslipId = btn.dataset.payslipId;
                const payslipType = btn.dataset.payslipType;
                this.showEmailSelection(payslipId, payslipType, btn);
            }
        });
        
        // Email confirmation
        const confirmBtn = document.getElementById('confirm-email-send');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                this.sendPayslipEmail();
            });
        }
        
        // Modal close events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                const modal = e.target.closest('.modal');
                if (modal) this.closeModal(modal.id);
            }
        });
        
        // ESC key handlers
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) this.closeModal(openModal.id);
            }
        });
    }
    
    setupTabSwitching() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.dataset.tab;
                
                // Remove active class from all tabs and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                button.classList.add('active');
                const targetContent = document.getElementById(targetTab);
                if (targetContent) {
                    targetContent.classList.add('active');
                    
                    // Add smooth transition animation
                    targetContent.style.opacity = '0';
                    targetContent.style.transform = 'translateY(20px)';
                    
                    setTimeout(() => {
                        targetContent.style.transition = 'all 0.3s ease';
                        targetContent.style.opacity = '1';
                        targetContent.style.transform = 'translateY(0)';
                    }, 50);
                }
            });
        });
    }
    
    showEmailSelection(payslipId, payslipType, buttonElement) {
        // Get payslip information from the card
        const payslipCard = buttonElement.closest('.payslip-card');
        let payslipInfo = {
            employee: '{{ request.user.name }}',
            period: 'N/A',
            type: payslipType
        };
        
        if (payslipCard) {
            const headerElement = payslipCard.querySelector('.card-header h4');
            if (headerElement) {
                payslipInfo.period = headerElement.textContent.trim();
            }
            
            const typeElement = payslipCard.querySelector('.payslip-type-badge');
            if (typeElement) {
                payslipInfo.type = typeElement.textContent.trim();
            }
        }
        
        // Setup email options (these would normally come from the server)
        const emailOptions = [
            { value: 'personal', label: 'Personal Email ({{ request.user.email }})' },
            { value: 'work', label: 'Work Email' }
        ];
        
        const radioContainer = document.getElementById('email-options');
        if (!radioContainer) return;
        
        radioContainer.innerHTML = '';
        
        emailOptions.forEach((option, index) => {
            const radioDiv = document.createElement('div');
            radioDiv.innerHTML = `
                <label class="label">
                    <input type="radio" name="email_type" value="${option.value}" ${index === 0 ? 'checked' : ''}>
                    <p class="text">${option.label}</p>
                </label>
            `;
            radioContainer.appendChild(radioDiv);
        });
        
        // Update payslip info in modal
        this.updatePayslipInfo(payslipInfo);
        
        // Store payslip info for sending
        this.currentPayslip = { 
            id: payslipId, 
            type: payslipType,
            info: payslipInfo
        };
        
        this.openModal('emailSelectionModal');
    }
    
    updatePayslipInfo(payslipInfo) {
        const employeeNameElement = document.getElementById('payslip-employee-name');
        const periodElement = document.getElementById('payslip-period');
        const typeElement = document.getElementById('payslip-type');
        const payslipInfoContainer = document.getElementById('payslip-info');
        
        if (employeeNameElement) employeeNameElement.textContent = payslipInfo.employee;
        if (periodElement) periodElement.textContent = payslipInfo.period;
        if (typeElement) typeElement.textContent = payslipInfo.type;
        if (payslipInfoContainer) payslipInfoContainer.style.display = 'block';
    }
    
    async sendPayslipEmail() {
        const selectedEmail = document.querySelector('input[name="email_type"]:checked');
        if (!selectedEmail || !this.currentPayslip) {
            this.showToast('Please select an email option', 'warning');
            return;
        }
        
        const confirmBtn = document.getElementById('confirm-email-send');
        if (confirmBtn) {
            confirmBtn.classList.add('btn-loading');
            confirmBtn.disabled = true;
            
            // Update button text
            const originalText = confirmBtn.innerHTML;
            confirmBtn.innerHTML = '<div class="loading-spinner"></div> Sending...';
        }
        
        try {
            const formData = new FormData();
            formData.append('email_type', selectedEmail.value);
            
            const response = await fetch(`/finance/payslip/send/${this.currentPayslip.id}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showToast(data.message, 'success');
                this.closeModal('emailSelectionModal');
                
                // Add visual feedback to the button that was clicked
                this.markPayslipAsSent();
            } else {
                this.showToast(data.message || 'Failed to send email', 'error');
            }
        } catch (error) {
            console.error('Error sending email:', error);
            this.showToast('Error sending email', 'error');
        } finally {
            if (confirmBtn) {
                confirmBtn.classList.remove('btn-loading');
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = '<i class="fas fa-envelope"></i> Send Payslip';
            }
        }
    }
    
    markPayslipAsSent() {
        // Find all payslip buttons with the same ID and mark them as sent
        const buttons = document.querySelectorAll(`[data-payslip-id="${this.currentPayslip.id}"]`);
        buttons.forEach(btn => {
            btn.classList.add('btn-success');
            btn.classList.remove('btn-primary');
            btn.innerHTML = '<i class="fas fa-check"></i> Sent';
            
            // Reset after 3 seconds
            setTimeout(() => {
                btn.classList.remove('btn-success');
                btn.classList.add('btn-primary');
                btn.innerHTML = '<i class="fas fa-envelope"></i> Send to Email';
            }, 3000);
        });
    }
    
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
            modal.style.opacity = '1';
            modal.style.visibility = 'visible';
            document.body.style.overflow = 'hidden';
            
            // Add entrance animation
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.transform = 'scale(0.9) translateY(-20px)';
                modalContent.style.opacity = '0';
                
                setTimeout(() => {
                    modalContent.style.transition = 'all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
                    modalContent.style.transform = 'scale(1) translateY(0)';
                    modalContent.style.opacity = '1';
                }, 50);
            }
        }
    }
    
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            const modalContent = modal.querySelector('.modal-content');
            
            if (modalContent) {
                modalContent.style.transition = 'all 0.2s ease';
                modalContent.style.transform = 'scale(0.9) translateY(-20px)';
                modalContent.style.opacity = '0';
            }
            
            setTimeout(() => {
                modal.classList.remove('show');
                modal.style.opacity = '0';
                modal.style.visibility = 'hidden';
                document.body.style.overflow = '';
                
                // Reset payslip info
                const payslipInfoContainer = document.getElementById('payslip-info');
                if (payslipInfoContainer) payslipInfoContainer.style.display = 'none';
                
                // Reset form
                const form = document.getElementById('email-selection-form');
                if (form) form.reset();
                
                this.currentPayslip = null;
            }, 200);
        }
    }
    
    showToast(message, type = 'info') {
        // Use the existing toast notification system from the project
        if (window.portalUI && window.portalUI.showNotification) {
            window.portalUI.showNotification(message, type);
        } else {
            // Fallback if toast system is not available
            console.log(`${type.toUpperCase()}: ${message}`);
            
            // Create a simple toast fallback
            this.createSimpleToast(message, type);
        }
    }
    
    createSimpleToast(message, type) {
        // Simple toast fallback implementation
        const toast = document.createElement('div');
        toast.className = `simple-toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${this.getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;
        
        // Add styles
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${this.getToastColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            animation: slideInToast 0.3s ease;
        `;
        
        document.body.appendChild(toast);
        
        // Remove after 4 seconds
        setTimeout(() => {
            toast.style.animation = 'slideOutToast 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 4000);
        
        // Add animation styles if not already present
        this.addToastAnimations();
    }
    
    getToastIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
    
    getToastColor(type) {
        const colors = {
            'success': '#28a745',
            'error': '#dc3545',
            'warning': '#ffc107',
            'info': '#17a2b8'
        };
        return colors[type] || '#17a2b8';
    }
    
    addToastAnimations() {
        if (!document.getElementById('toast-animations')) {
            const style = document.createElement('style');
            style.id = 'toast-animations';
            style.textContent = `
                @keyframes slideInToast {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                @keyframes slideOutToast {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
                .simple-toast .toast-content {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    // Utility method to format currency
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP'
        }).format(amount);
    }
    
    // Utility method to format dates
    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-PH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
}

// Initialize the employee finance module when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.employeeFinance = new EmployeeFinanceModule();
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EmployeeFinanceModule;
}