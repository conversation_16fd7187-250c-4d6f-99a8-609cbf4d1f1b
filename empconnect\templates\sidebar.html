{% load static %}
<aside class="sidebar minimized" id="sidebar">
    <div class="sidebar-header">
        <div class="logo-container" id="logo-container">
            <img src="{% static "images/icon/gitlab.svg" %}" alt="REPConnect Logo" class="logo" />
            <div class="logo-info">
                <span class="logo-text">REPConnect</span>
                <span class="version-text">v1.1.0</span>
            </div>
        </div>
        <button id="sidebar-toggle" class="toggle-btn">
        <!-- <i class="fas fa-chevron-right"></i> -->
        <svg
            xmlns="http://www.w3.org/2000/svg"
            height="24px"
            viewBox="0 -960 960 960"
            width="24px"
            fill="gray"
        >
            <path
            d="M200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm120-80v-560H200v560h120Zm80 0h360v-560H400v560Zm-80 0H200h120Z"
            />
        </svg>
        </button>
    </div>

    <nav class="sidebar-nav">
        <ul>
            <li class="nav-item{% if request.path == '/overview/' %} active{% endif %}" data-url="{% url 'overview' %}">
                <a href="{% url "overview" %}">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    height="28px"
                    viewBox="0 -960 960 960"
                    width="28px"
                    fill="currentColor"
                >
                    <path
                    d="M530-636.34v-147.31q0-15.66 10.43-26Q550.86-820 566.27-820h217.62q15.42 0 25.76 10.35 10.35 10.34 10.35 26v147.31q0 15.65-10.43 25.99Q799.14-600 783.73-600H566.11q-15.42 0-25.76-10.35Q530-620.69 530-636.34ZM140-496v-288.01q0-15.3 10.43-25.64Q160.86-820 176.27-820h217.62q15.42 0 25.76 10.35Q430-799.3 430-784v288.01q0 15.3-10.43 25.64Q409.14-460 393.73-460H176.11q-15.42 0-25.76-10.35Q140-480.7 140-496Zm390 320v-288.01q0-15.3 10.43-25.64Q550.86-500 566.27-500h217.62q15.42 0 25.76 10.35Q820-479.3 820-464v288.01q0 15.3-10.43 25.64Q799.14-140 783.73-140H566.11q-15.42 0-25.76-10.35Q530-160.7 530-176Zm-390-.35v-147.31q0-15.65 10.43-25.99Q160.86-360 176.27-360h217.62q15.42 0 25.76 10.35Q430-339.31 430-323.66v147.31q0 15.66-10.43 26Q409.14-140 393.73-140H176.11q-15.42 0-25.76-10.35-10.35-10.34-10.35-26ZM200-520h170v-240H200v240Zm390 320h170v-240H590v240Zm0-460h170v-100H590v100ZM200-200h170v-100H200v100Zm170-320Zm220-140Zm0 220ZM370-300Z"
                    />
                </svg>
                <span class="nav-text">Overview</span>
                </a>
            </li>

            <li class="nav-item{% if request.path == '/news/' %} active{% endif %}" data-url="#">
                <a href="#"> 
                <svg xmlns="http://www.w3.org/2000/svg" height="28px" viewBox="0 -960 960 960" width="28px" fill="currentColor">
                    <path d="M172.31-140Q142-140 121-161q-21-21-21-51.31v-535.38Q100-778 121-799q21-21 51.31-21h615.38Q818-820 839-799q21 21 21 51.31v535.38Q860-182 839-161q-21 21-51.31 21H172.31Zm0-60h615.38q4.62 0 8.46-3.85 3.85-3.84 3.85-8.46v-535.38q0-4.62-3.85-8.46-3.84-3.85-8.46-3.85H172.31q-4.62 0-8.46 3.85-3.85 3.84-3.85 8.46v535.38q0 4.62 3.85 8.46 3.84 3.85 8.46 3.85Zm113.08-95.39h389.22q12.77 0 21.39-8.61 8.61-8.62 8.61-21.39 0-12.76-8.61-21.38-8.62-8.61-21.39-8.61H285.39q-12.77 0-21.39 8.61-8.61 8.62-8.61 21.38 0 12.77 8.61 21.39 8.62 8.61 21.39 8.61Zm0-154.61h87.69q12.77 0 21.38-8.62 8.62-8.61 8.62-21.38v-154.61q0-12.77-8.62-21.39-8.61-8.61-21.38-8.61h-87.69q-12.77 0-21.39 8.61-8.61 8.62-8.61 21.39V-480q0 12.77 8.61 21.38 8.62 8.62 21.39 8.62Zm235.38 0h153.84q12.77 0 21.39-8.62 8.61-8.61 8.61-21.38T696-501.38q-8.62-8.62-21.39-8.62H520.77q-12.77 0-21.38 8.62-8.62 8.61-8.62 21.38t8.62 21.38Q508-450 520.77-450Zm0-154.62h153.84q12.77 0 21.39-8.61 8.61-8.62 8.61-21.38 0-12.77-8.61-21.39-8.62-8.61-21.39-8.61H520.77q-12.77 0-21.38 8.61-8.62 8.62-8.62 21.39 0 12.76 8.62 21.38 8.61 8.61 21.38 8.61ZM160-200v-560 560Z"/>
                </svg>
                <span class="nav-text">The Wire News</span>
                </a>
            </li>

            <li class="nav-item{% if request.path == '/calendar/' %} active{% endif %}" data-url="{% url "calendar_view" %}">   
                <a href="{% url "calendar_view" %}">             
                <svg xmlns="http://www.w3.org/2000/svg" height="28px" viewBox="0 -960 960 960" width="28px" fill="currentColor">
                    <path d="M212.31-100Q182-100 161-121q-21-21-21-51.31v-535.38Q140-738 161-759q21-21 51.31-21h55.38v-53.85q0-13.15 8.81-21.96 8.81-8.8 21.96-8.8 13.16 0 21.96 8.8 8.81 8.81 8.81 21.96V-780h303.08v-54.61q0-12.77 8.61-21.39 8.62-8.61 21.39-8.61 12.77 0 21.38 8.61 8.62 8.62 8.62 21.39V-780h55.38Q778-780 799-759q21 21 21 51.31v535.38Q820-142 799-121q-21 21-51.31 21H212.31Zm0-60h535.38q4.62 0 8.46-3.85 3.85-3.84 3.85-8.46v-375.38H200v375.38q0 4.62 3.85 8.46 3.84 3.85 8.46 3.85ZM200-607.69h560v-100q0-4.62-3.85-8.46-3.84-3.85-8.46-3.85H212.31q-4.62 0-8.46 3.85-3.85 3.84-3.85 8.46v100Zm0 0V-720v112.31Zm280 210.77q-14.69 0-25.04-10.35-10.34-10.34-10.34-25.04 0-14.69 10.34-25.04 10.35-10.34 25.04-10.34t25.04 10.34q10.34 10.35 10.34 25.04 0 14.7-10.34 25.04-10.35 10.35-25.04 10.35Zm-160 0q-14.69 0-25.04-10.35-10.34-10.34-10.34-25.04 0-14.69 10.34-25.04 10.35-10.34 25.04-10.34t25.04 10.34q10.34 10.35 10.34 25.04 0 14.7-10.34 25.04-10.35 10.35-25.04 10.35Zm320 0q-14.69 0-25.04-10.35-10.34-10.34-10.34-25.04 0-14.69 10.34-25.04 10.35-10.34 25.04-10.34t25.04 10.34q10.34 10.35 10.34 25.04 0 14.7-10.34 25.04-10.35 10.35-25.04 10.35ZM480-240q-14.69 0-25.04-10.35-10.34-10.34-10.34-25.03 0-14.7 10.34-25.04 10.35-10.35 25.04-10.35t25.04 10.35q10.34 10.34 10.34 25.04 0 14.69-10.34 25.03Q494.69-240 480-240Zm-160 0q-14.69 0-25.04-10.35-10.34-10.34-10.34-25.03 0-14.7 10.34-25.04 10.35-10.35 25.04-10.35t25.04 10.35q10.34 10.34 10.34 25.04 0 14.69-10.34 25.03Q334.69-240 320-240Zm320 0q-14.69 0-25.04-10.35-10.34-10.34-10.34-25.03 0-14.7 10.34-25.04 10.35-10.35 25.04-10.35t25.04 10.35q10.34 10.34 10.34 25.04 0 14.69-10.34 25.03Q654.69-240 640-240Z"/>
                </svg>
                <span class="nav-text">Calendar</span>
                </a>
            </li>

            <li class="nav-item{% if request.path == '/prf/' %} active{% endif %}" data-url="{% if request.user.hr_admin %}{% url "admin_prf" %}{% else %}{% url "user_prf" %}{% endif %}">   
                <a href="{% if request.user.hr_admin %}{% url "admin_prf" %}{% else %}{% url "user_prf" %}{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" height="28px" viewBox="0 -960 960 960" width="28px" fill="currentColor">
                    <path d="M212.31-140q-29.83 0-51.07-21.24Q140-182.48 140-212.31v-535.38q0-29.83 21.24-51.07Q182.48-820 212.31-820h178q3.77-33.31 29.08-56.65 25.3-23.35 60.8-23.35T541-876.65q25.31 23.34 28.69 56.65h178q29.83 0 51.07 21.24Q820-777.52 820-747.69v535.38q0 29.83-21.24 51.07Q777.52-140 747.69-140H212.31Zm0-60h535.38q4.62 0 8.46-3.85 3.85-3.84 3.85-8.46v-535.38q0-4.62-3.85-8.46-3.84-3.85-8.46-3.85H212.31q-4.62 0-8.46 3.85-3.85 3.84-3.85 8.46v535.38q0 4.62 3.85 8.46 3.84 3.85 8.46 3.85ZM320-294.62h200q12.75 0 21.37-8.63 8.63-8.62 8.63-21.38 0-12.75-8.63-21.37-8.62-8.61-21.37-8.61H320q-12.75 0-21.37 8.62-8.63 8.63-8.63 21.39 0 12.75 8.63 21.37 8.62 8.61 21.37 8.61ZM320-450h320q12.75 0 21.37-8.63 8.63-8.63 8.63-21.38 0-12.76-8.63-21.37Q652.75-510 640-510H320q-12.75 0-21.37 8.63-8.63 8.63-8.63 21.38 0 12.76 8.63 21.37Q307.25-450 320-450Zm0-155.39h320q12.75 0 21.37-8.62 8.63-8.63 8.63-21.39 0-12.75-8.63-21.37-8.62-8.61-21.37-8.61H320q-12.75 0-21.37 8.63-8.63 8.62-8.63 21.38 0 12.75 8.63 21.37 8.62 8.61 21.37 8.61Zm160-180.76q13 0 21.5-8.5t8.5-21.5q0-13-8.5-21.5t-21.5-8.5q-13 0-21.5 8.5t-8.5 21.5q0 13 8.5 21.5t21.5 8.5ZM200-200v-560 560Z"/>
                </svg>
                <span class="nav-text">PR-Form</span>
                </a>
            </li>

            <li class="nav-item{% if request.path == '/certificate/' %} active{% endif %}" data-url="{% if request.user.hr_admin %}{% url "admin_cert" %}{% else %}{% url "user_cert" %}{% endif %}">
                <a href="{% if request.user.hr_admin %}{% url "admin_cert" %}{% else %}{% url "user_cert" %}{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" height="28px" viewBox="0 -960 960 960" width="28px" fill="currentColor">
                    <path d="m480-488-65.31 50.46q-5.61 4.23-11.04.31-5.42-3.92-3.42-10.16l24.85-81.69-66.16-50.61q-5-4.23-2.8-10.35 2.19-6.11 8.42-6.11h81.77l24.84-80.46q2-6.24 8.85-6.24t8.85 6.24l24.84 80.46h81.16q6.23 0 8.73 6.11 2.5 6.12-2.5 10.35l-66.77 50.61 24.84 81.69q2 6.24-3.42 10.16t-11.04-.31L480-488Zm0 347.23L307.23-86.08q-17.69 6.23-32.46-4.81T260-120v-237.08q-38-39.69-59-91.77-21-52.07-21-111.15 0-125.54 87.23-212.77T480-860q125.54 0 212.77 87.23T780-560q0 59.08-21 111.15-21 52.08-59 91.77V-120q0 18.07-14.77 29.11t-32.46 4.81L480-140.77ZM480-320q100 0 170-70t70-170q0-100-70-170t-170-70q-100 0-170 70t-70 170q0 100 70 170t170 70ZM320-158.62l160-42.92 160 42.92v-148.61q-34.23 22.31-74.92 34.77Q524.38-260 480-260q-44.38 0-85.08-12.46-40.69-12.46-74.92-34.77v148.61Zm160-74.3Z"/>
                </svg>
                <span class="nav-text">Certificate</span>
                </a>
            </li>

            <li class="nav-item" data-url="#">
                <a href="#">
                <svg xmlns="http://www.w3.org/2000/svg" height="28px" viewBox="0 -960 960 960" width="28px" fill="currentColor">
                    <path d="M324.62-480q-12.77 0-21.39 8.62-8.61 8.61-8.61 21.38v130q0 12.77 8.61 21.38 8.62 8.62 21.39 8.62 12.76 0 21.38-8.62 8.61-8.61 8.61-21.38v-130q0-12.77-8.61-21.38-8.62-8.62-21.38-8.62Zm310.76-200q-12.76 0-21.38 8.62-8.61 8.61-8.61 21.38v330q0 12.77 8.61 21.38 8.62 8.62 21.38 8.62 12.77 0 21.39-8.62 8.61-8.61 8.61-21.38v-330q0-12.77-8.61-21.38-8.62-8.62-21.39-8.62ZM480-400q-12.77 0-21.38 8.62Q450-382.77 450-370v50q0 12.77 8.62 21.38Q467.23-290 480-290t21.38-8.62Q510-307.23 510-320v-50q0-12.77-8.62-21.38Q492.77-400 480-400ZM212.31-140Q182-140 161-161q-21-21-21-51.31v-535.38Q140-778 161-799q21-21 51.31-21h535.38Q778-820 799-799q21 21 21 51.31v535.38Q820-182 799-161q-21 21-51.31 21H212.31Zm0-60h535.38q4.62 0 8.46-3.85 3.85-3.84 3.85-8.46v-535.38q0-4.62-3.85-8.46-3.84-3.85-8.46-3.85H212.31q-4.62 0-8.46 3.85-3.85 3.84-3.85 8.46v535.38q0 4.62 3.85 8.46 3.84 3.85 8.46 3.85ZM200-760v560-560Zm280 270q12.77 0 21.38-8.62Q510-507.23 510-520t-8.62-21.38Q492.77-550 480-550t-21.38 8.62Q450-532.77 450-520t8.62 21.38Q467.23-490 480-490Z"/>
                </svg>
                <span class="nav-text">Assessments</span>
                </a>
            </li>

            <li class="nav-item{% if request.path == '/finance/' %} active{% endif %}" data-url="{% if request.user.accounting_admin %}{% url "admin_finance" %}{% else %}{% url "user_finance" %}{% endif %}">
                <a href="{% if request.user.accounting_admin %}{% url "admin_finance" %}{% else %}{% url "user_finance" %}{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" height="28px" viewBox="0 -960 960 960" width="28px" fill="currentColor">
                    <path d="M200-240v40-560 520Zm12.31 100q-29.92 0-51.12-21.19Q140-182.39 140-212.31v-535.38q0-29.92 21.19-51.12Q182.39-820 212.31-820h535.38q29.92 0 51.12 21.19Q820-777.61 820-747.69v108.85h-60v-108.85q0-5.39-3.46-8.85t-8.85-3.46H212.31q-5.39 0-8.85 3.46t-3.46 8.85v535.38q0 5.39 3.46 8.85t8.85 3.46h535.38q5.39 0 8.85-3.46t3.46-8.85v-108.85h60v108.85q0 29.92-21.19 51.12Q777.61-140 747.69-140H212.31Zm320-160q-29.92 0-51.12-21.19Q460-342.39 460-372.31v-215.38q0-29.92 21.19-51.12Q502.39-660 532.31-660h255.38q29.92 0 51.12 21.19Q860-617.61 860-587.69v215.38q0 29.92-21.19 51.12Q817.61-300 787.69-300H532.31Zm255.38-60q5.39 0 8.85-3.46t3.46-8.85v-215.38q0-5.39-3.46-8.85t-8.85-3.46H532.31q-5.39 0-8.85 3.46t-3.46 8.85v215.38q0 5.39 3.46 8.85t8.85 3.46h255.38ZM640-420q25 0 42.5-17.5T700-480q0-25-17.5-42.5T640-540q-25 0-42.5 17.5T580-480q0 25 17.5 42.5T640-420Z"/>
                </svg>
                <span class="nav-text">Finances</span>
                </a>
            </li>

            <li class="nav-item" data-url="#">
                <a href="#">
                <svg xmlns="http://www.w3.org/2000/svg" height="28px" viewBox="0 -960 960 960" width="28px" fill="currentColor">
                    <path d="M280-290h200q12.77 0 21.38-8.62Q510-307.23 510-320t-8.62-21.38Q492.77-350 480-350H280q-12.77 0-21.38 8.62Q250-332.77 250-320t8.62 21.38Q267.23-290 280-290Zm400-380q-12.77 0-21.38 8.62Q650-652.77 650-640v320q0 12.77 8.62 21.38Q667.23-290 680-290t21.38-8.62Q710-307.23 710-320v-320q0-12.77-8.62-21.38Q692.77-670 680-670ZM280-450h200q12.77 0 21.38-8.62Q510-467.23 510-480t-8.62-21.38Q492.77-510 480-510H280q-12.77 0-21.38 8.62Q250-492.77 250-480t8.62 21.38Q267.23-450 280-450Zm0-160h200q12.77 0 21.38-8.62Q510-627.23 510-640t-8.62-21.38Q492.77-670 480-670H280q-12.77 0-21.38 8.62Q250-652.77 250-640t8.62 21.38Q267.23-610 280-610ZM172.31-140Q142-140 121-161q-21-21-21-51.31v-535.38Q100-778 121-799q21-21 51.31-21h615.38Q818-820 839-799q21 21 21 51.31v535.38Q860-182 839-161q-21 21-51.31 21H172.31Zm0-60h615.38q4.62 0 8.46-3.85 3.85-3.84 3.85-8.46v-535.38q0-4.62-3.85-8.46-3.84-3.85-8.46-3.85H172.31q-4.62 0-8.46 3.85-3.85 3.84-3.85 8.46v535.38q0 4.62 3.85 8.46 3.84 3.85 8.46 3.85ZM160-200v-560 560Z"/>
                </svg>
                <span class="nav-text">Leave Requests</span>
                </a>
            </li>

            {% if request.user.hr_admin %}
            <li class="nav-item{% if request.path == '/profile/admin/employees/' %} active{% endif %}" data-url="{% if request.user.hr_admin %}{% url "admin_employees" %}{% endif %}">
                <a href="{% url "admin_employees" %}">
                <svg xmlns="http://www.w3.org/2000/svg" height="28px" viewBox="0 -960 960 960" width="28px" fill="currentcolor">
                    <path d="M200-238.31q54-53 125.5-83.5t154.5-30.5q83 0 154.5 30.5t125.5 83.5v-509.38q0-4.62-3.85-8.46-3.84-3.85-8.46-3.85H212.31q-4.62 0-8.46 3.85-3.85 3.84-3.85 8.46v509.38Zm280-200.15q54.15 0 92.08-37.92Q610-514.31 610-568.46t-37.92-92.08q-37.93-37.92-92.08-37.92t-92.08 37.92Q350-622.61 350-568.46t37.92 92.08q37.93 37.92 92.08 37.92ZM212.31-140Q182-140 161-161q-21-21-21-51.31v-535.38Q140-778 161-799q21-21 51.31-21h535.38Q778-820 799-799q21 21 21 51.31v535.38Q820-182 799-161q-21 21-51.31 21H212.31Zm34.77-60h465.84Q662-246.69 601.89-269.5 541.77-292.31 480-292.31q-61 0-122.12 22.81-61.11 22.81-110.8 69.5ZM480-498.46q-28.85 0-49.42-20.58Q410-539.61 410-568.46t20.58-49.42q20.57-20.58 49.42-20.58t49.42 20.58Q550-597.31 550-568.46t-20.58 49.42q-20.57 20.58-49.42 20.58Zm0-.69Z"/>
                </svg>
                <span class="nav-text">Employees</span>
                </a>
            </li>
            {% endif %}

            <li class="nav-item" data-url="#">
                <a href="#">
                <svg xmlns="http://www.w3.org/2000/svg" height="28px" viewBox="0 -960 960 960" width="28px" fill="currentColor">
                    <path d="M172.31-180q-29.83 0-51.07-21.24Q100-222.48 100-252.31v-109.61q0-9.85 5.66-17.46 5.65-7.62 15.11-10.39 26.31-12.23 42.77-36.32Q180-450.19 180-480.02t-16.46-53.9q-16.46-24.08-42.77-36.31-9.46-2.77-15.11-10.39-5.66-7.61-5.66-17.46v-109.61q0-29.83 21.24-51.07Q142.48-780 172.31-780h615.38q29.83 0 51.07 21.24Q860-737.52 860-707.69v109.61q0 9.85-5.66 17.46-5.65 7.62-15.11 10.39-26.31 12.23-42.77 36.32Q780-509.81 780-479.98t16.46 53.9q16.46 24.08 42.77 36.31 9.46 2.77 15.11 10.39 5.66 7.61 5.66 17.46v109.61q0 29.83-21.24 51.07Q817.52-180 787.69-180H172.31Zm0-60h615.38q5.39 0 8.85-3.46t3.46-8.85V-342q-37-22-58.5-58.5T720-480q0-43 21.5-79.5T800-618v-89.69q0-5.39-3.46-8.85t-8.85-3.46H172.31q-5.39 0-8.85 3.46t-3.46 8.85V-618q37 22 58.5 58.5T240-480q0 43-21.5 79.5T160-342v89.69q0 5.39 3.46 8.85t8.85 3.46Zm307.7-53.85q12.76 0 21.37-8.63 8.62-8.62 8.62-21.38t-8.63-21.37q-8.63-8.62-21.38-8.62-12.76 0-21.37 8.63-8.62 8.63-8.62 21.39 0 12.75 8.63 21.37 8.63 8.61 21.38 8.61Zm0-156.15q12.76 0 21.37-8.63 8.62-8.63 8.62-21.38 0-12.76-8.63-21.37-8.63-8.62-21.38-8.62-12.76 0-21.37 8.63-8.62 8.63-8.62 21.38 0 12.76 8.63 21.37 8.63 8.62 21.38 8.62Zm0-156.15q12.76 0 21.37-8.63 8.62-8.63 8.62-21.39 0-12.75-8.63-21.37-8.63-8.61-21.38-8.61-12.76 0-21.37 8.63-8.62 8.62-8.62 21.38t8.63 21.37q8.63 8.62 21.38 8.62ZM480-480Z"/>
                </svg>
                <span class="nav-text">MIS Ticket</span>
                </a>
            </li>
        </ul>
    </nav>

    <div class="sidebar-footer">
        <div class="user-profile" id="user-profile">
        <div class="user-info">
            <img src="{{request.user.avatar.url}}" alt="User Avatar" class="avatar" />
            <div class="user-details">
            <span class="user-name">{{request.user.username}}</span>
            <span class="user-department">{{request.user.email}}</span>
            </div>
        </div>

        <div class="dropdown-menu" id="dropdown-menu">
            <div class="dropdown-header">
            <img src="{{request.user.avatar.url}}" alt="User Avatar" class="avatar" />
            <div class="dropdown-user-details">
                <span class="user-name">{{request.user.username}}</span>
                <span class="user-email">{{request.user.email}}</span>
            </div>
            </div>
            <ul>
            {% if not request.user.wire_admin and not request.user.clinic_admin and not request.user.iad_admin and not request.user.accounting_admin and not request.user.hr_admin and not request.user.hr_manager and not request.user.mis_admin %}
                <li>
                    <a href="{% url "user_profile" %}">
                        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px">
                            <path d="M0 0h24v24H0V0z" fill="none" />
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 
                            3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 
                            3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 
                            4-3.08 6-3.08 1.99 0 5.97 1.09 
                            6 3.08-1.29 1.94-3.5 3.22-6 3.22z" />
                        </svg>
                        <span>Profile Settings</span>
                    </a>
                </li>
            {% endif %}
            
            <li>
                <a href="{% url "change_password" %}">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    height="24px"
                    viewBox="0 0 24 24"
                    width="24px"
                >
                    <path d="M0 0h24v24H0V0z" fill="none" />
                    <path
                    d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zM9 8V6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9z"
                    />
                </svg>
                <span>Password Settings</span>
                </a>
            </li>
            <li>
                <a href="{% url "user-logout" %}">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    height="24px"
                    viewBox="0 0 24 24"
                    width="24px"
                >
                    <path d="M0 0h24v24H0V0z" fill="none" />
                    <path
                    d="M17.77 3.23L16.34 4.66c.97.97 1.56 2.28 1.56 3.71s-.59 2.74-1.56 3.71l1.43 1.43c1.33-1.33 2.13-3.11 2.13-5.14s-.8-3.81-2.13-5.14zM10.5 15H13v-2h-2.5c-1.1 0-2-.9-2-2V9c0-1.1.9-2 2-2h2.5V5H10c-2.21 0-4 1.79-4 4v2c0 2.21 1.79 4 4 4zM15 15h2.5c1.1 0 2-.9 2-2V9c0-1.1-.9-2-2-2H15v2h2.5v2H15v2zM6.23 3.23C4.9 4.56 4.1 6.34 4.1 8.37s.8 3.81 2.13 5.14l1.43-1.43c-.97-.97-1.56-2.28-1.56-3.71s.59-2.74 1.56-3.71L6.23 3.23z"
                    />
                </svg>
                <span>Log Out</span>
                </a>
            </li>
            </ul>
            <div class="dropdown-divider"></div>
            <div class="dropdown-theme-toggle">
            <span>Dark Theme</span>
            <label class="toggle-switch">
                <input type="checkbox" id="theme-toggle-checkbox" />
                <span class="slider"></span>
            </label>
            </div>
        </div>
        </div>
    </div>
</aside>
