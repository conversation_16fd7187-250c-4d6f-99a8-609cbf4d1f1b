{% extends "main.html" %}
{% load static %}

{% block title %}REPConnect - Time Logs Management{% endblock title %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/calendar/timelogs.css' %}">
{% endblock %}

{% block content %}
<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
<div class="page-content" id="page-content">
    <!-- Header Section -->
    <div class="timelogs-header">
            <div class="header-content">
                <h2>Time Logs Management</h2>
                <p>Import, manage, and track employee time logs</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-outline" onclick="window.location.href='{% url 'calendar_view' %}'">
                    <i class="fas fa-arrow-left"></i>
                    Back to Calendar
                </button>
            </div>
    </div>

    <!-- Employee Time Logs List -->
    <section class="component-section">
        <div class="component-card">
            <!-- Search and Filter Section -->
            <div class="search-filter-container">
                <div class="search-container">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" placeholder="Search employees..." class="search-input" id="employeeSearch">
                        <button class="search-clear" id="searchClear">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="filter-actions">
                    <button class="btn btn-primary" id="importBtn">
                        <i class="fas fa-upload"></i>
                        Import Excel
                    </button>
                </div>
            </div>
            
            <div class="employee-list" id="employeeList">
                <!-- Employee items will be populated by JavaScript -->
                <div class="loading-state" id="loadingState">
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                        <p>Loading employee time logs...</p>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <div class="pagination-container" id="paginationContainer" style="display: none;">
                <div class="pagination-info">
                    <span id="paginationInfo">Showing 1-10 of 100 employees</span>
                </div>
                <div class="pagination-controls">
                    <button class="btn btn-outline btn-sm" id="prevPageBtn" disabled>
                        <i class="fas fa-chevron-left"></i>
                        Previous
                    </button>
                    <div class="page-numbers" id="pageNumbers">
                        <!-- Page numbers will be generated by JavaScript -->
                    </div>
                    <button class="btn btn-outline btn-sm" id="nextPageBtn">
                        Next
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Import Time Logs Modal -->
<div id="importTimelogModal" class="modal modal-lg">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3>Import Time Logs</h3>
            <button class="modal-close" onclick="closeModal('importTimelogModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="import-actions-header">
                <p class="import-description">Upload Excel files containing employee time log data. You can download a template to ensure proper formatting.</p>
                <button class="btn btn-outline" id="exportTemplateBtn">
                    <i class="fas fa-download"></i>
                    Download Template
                </button>
            </div>

            <div class="file-upload-area">
                <div class="file-upload">
                    <input type="file" id="file-upload" class="file-input" accept=".xlsx,.xls">
                    <label for="file-upload" class="file-label">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <span>Drag & drop Excel files here or click to browse</span>
                        <small>Supported formats: .xlsx, .xls (Max size: 10MB)</small>
                    </label>
                </div>

                <div class="selected-files" id="selectedFiles" style="display: none;">
                    <h4>Selected Files:</h4>
                    <div class="file-list" id="fileList">
                        <!-- Selected files will be displayed here -->
                    </div>
                </div>

                <div class="upload-progress" id="uploadProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <span class="progress-text" id="progressText">0%</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" id="uploadBtn" disabled>
                <i class="fas fa-upload"></i>
                Upload Files
            </button>
            <button class="btn btn-outline" onclick="closeModal('importTimelogModal')">
                Cancel
            </button>
        </div>
    </div>
</div>

<!-- Edit Time Log Modal -->
<div id="editTimelogModal" class="modal modal-md">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3>Edit Time Log</h3>
            <button class="modal-close" onclick="closeModal('editTimelogModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="editTimelogForm">
                <input type="hidden" id="editTimelogId">
                <div class="form-group">
                    <label class="form-label">Date</label>
                    <input type="date" class="form-input" id="editDate" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Time</label>
                    <input type="time" class="form-input" id="editTime" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Entry Type</label>
                    <select class="form-input" id="editEntryType" required>
                        <option value="">Select Entry Type</option>
                        <option value="timein">Time In</option>
                        <option value="timeout">Time Out</option>
                    </select>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" id="saveTimelogBtn">
                <i class="fas fa-save"></i>
                Save Changes
            </button>
            <button class="btn btn-outline" onclick="closeModal('editTimelogModal')">
                Cancel
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteTimelogModal" class="modal modal-md">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3>Confirm Deletion</h3>
            <button class="modal-close" onclick="closeModal('deleteTimelogModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="delete-confirmation">
                <i class="fas fa-exclamation-triangle warning-icon"></i>
                <p>Are you sure you want to delete this time log entry?</p>
                <p class="delete-details" id="deleteDetails"></p>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-error" id="confirmDeleteBtn">
                <i class="fas fa-trash"></i>
                Delete
            </button>
            <button class="btn btn-outline" onclick="closeModal('deleteTimelogModal')">
                Cancel
            </button>
        </div>
    </div>
</div>

<!-- Add Time Log Modal -->
<div id="addTimelogModal" class="modal">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3>Add Time Log</h3>
            <button class="modal-close" onclick="closeModal('addTimelogModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="addTimelogForm" class="form">
                <div class="form-group">
                    <label class="form-label">Employee</label>
                    <select class="form-input" id="addEmployeeSelect" required>
                        <option value="">Select Employee</option>
                        <!-- Employee options will be populated by JavaScript -->
                    </select>
                </div>
                <div class="form-row form-row-2col">
                    <div class="form-group">
                        <label class="form-label">Date</label>
                        <input type="date" class="form-input" id="addDate" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Time</label>
                        <input type="time" class="form-input" id="addTime" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Entry Type</label>
                    <select class="form-input" id="addEntryType" required>
                        <option value="">Select Entry Type</option>
                        <option value="timein">Time In</option>
                        <option value="timeout">Time Out</option>
                    </select>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" id="addTimelogBtn">
                <i class="fas fa-plus"></i>
                Add Time Log
            </button>
            <button class="btn btn-outline" onclick="closeModal('addTimelogModal')">
                Cancel
            </button>
        </div>
    </div>
</div>
{% endblock content %}

{% block extra_js %}
<script src="{% static 'js/calendar/timelogs.js' %}"></script>
{% endblock %}