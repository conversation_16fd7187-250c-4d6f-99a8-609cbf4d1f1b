# Generated by Django 5.2.3 on 2025-07-07 04:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ContactPerson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('relationship', models.CharField(choices=[('Father', 'Father'), ('Mother', 'Mother'), ('Spouse', 'Spouse'), ('Sibling', 'Sibling'), ('Child', 'Child'), ('Friend', 'Friend'), ('Other', 'Other')], max_length=20)),
                ('contact_number', models.CharField(max_length=20)),
                ('block_lot', models.Char<PERSON><PERSON>(blank=True, max_length=50, null=True)),
                ('street', models.CharField(blank=True, max_length=100, null=True)),
                ('barangay', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('province', models.CharField(blank=True, max_length=100, null=True)),
                ('country', models.CharField(default='Philippines', max_length=100)),
                ('postal_code', models.CharField(blank=True, max_length=10, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='contact_person', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='EmploymentInformation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('position', models.CharField(max_length=100)),
                ('line', models.CharField(blank=True, choices=[('Welding 1', 'Welding 1'), ('Welding 2', 'Welding 2'), ('Assembly 1', 'Assembly 1'), ('Assembly 2', 'Assembly 2'), ('Quality Control', 'Quality Control'), ('Maintenance', 'Maintenance'), ('Office', 'Office')], max_length=50, null=True)),
                ('department', models.CharField(max_length=100)),
                ('employment_type', models.CharField(choices=[('Regular', 'Regular'), ('Probationary', 'Probationary'), ('OJT', 'OJT')], max_length=20)),
                ('date_hired', models.DateField()),
                ('tin_number', models.CharField(blank=True, max_length=15, null=True)),
                ('sss_number', models.CharField(blank=True, max_length=15, null=True)),
                ('hdmf_number', models.CharField(blank=True, max_length=15, null=True)),
                ('philhealth_number', models.CharField(blank=True, max_length=15, null=True)),
                ('bank_account', models.CharField(blank=True, max_length=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervised_employees', to=settings.AUTH_USER_MODEL)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='employment_info', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='FamilyBackground',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mother_name', models.CharField(blank=True, max_length=100, null=True)),
                ('father_name', models.CharField(blank=True, max_length=100, null=True)),
                ('spouse_name', models.CharField(blank=True, max_length=100, null=True)),
                ('children_names', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='family_background', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='PersonalInformation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('middle_name', models.CharField(blank=True, max_length=50, null=True)),
                ('nickname', models.CharField(blank=True, max_length=50, null=True)),
                ('work_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('gender', models.CharField(blank=True, choices=[('Male', 'Male'), ('Female', 'Female'), ('Other', 'Other')], max_length=10, null=True)),
                ('birth_date', models.DateField(blank=True, null=True)),
                ('birth_place', models.CharField(blank=True, max_length=200, null=True)),
                ('contact_number', models.CharField(blank=True, max_length=20, null=True)),
                ('present_block_lot', models.CharField(blank=True, max_length=50, null=True)),
                ('present_street', models.CharField(blank=True, max_length=100, null=True)),
                ('present_barangay', models.CharField(blank=True, max_length=100, null=True)),
                ('present_city', models.CharField(blank=True, max_length=100, null=True)),
                ('present_province', models.CharField(blank=True, max_length=100, null=True)),
                ('present_country', models.CharField(default='Philippines', max_length=100)),
                ('present_postal_code', models.CharField(blank=True, max_length=10, null=True)),
                ('provincial_block_lot', models.CharField(blank=True, max_length=50, null=True)),
                ('provincial_street', models.CharField(blank=True, max_length=100, null=True)),
                ('provincial_barangay', models.CharField(blank=True, max_length=100, null=True)),
                ('provincial_city', models.CharField(blank=True, max_length=100, null=True)),
                ('provincial_province', models.CharField(blank=True, max_length=100, null=True)),
                ('provincial_country', models.CharField(default='Philippines', max_length=100)),
                ('provincial_postal_code', models.CharField(blank=True, max_length=10, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='personal_info', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='EducationalBackground',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.CharField(choices=[('Primary', 'Primary'), ('Secondary', 'Secondary'), ('Tertiary', 'Tertiary'), ('Vocational', 'Vocational')], max_length=20)),
                ('school_name', models.CharField(max_length=200)),
                ('degree_course', models.CharField(blank=True, max_length=200, null=True)),
                ('year_graduated', models.IntegerField(blank=True, null=True)),
                ('honors_awards', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='education', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'level')},
            },
        ),
    ]
