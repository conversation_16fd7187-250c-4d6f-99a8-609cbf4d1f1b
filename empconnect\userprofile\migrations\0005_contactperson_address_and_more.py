# Generated by Django 5.2.3 on 2025-07-08 23:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('userprofile', '0004_remove_personalinformation_postal_code_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='contactperson',
            name='address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='contactperson',
            name='contact_country_code',
            field=models.CharField(blank=True, default='+63', max_length=5, null=True),
        ),
        migrations.AddField(
            model_name='personalinformation',
            name='contact_country_code',
            field=models.CharField(blank=True, default='+63', max_length=5, null=True),
        ),
    ]
