# Generated by Django 5.2.3 on 2025-07-09 23:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('generalsettings', '0003_position'),
        ('userprofile', '0005_contactperson_address_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='employmentinformation',
            name='department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='generalsettings.department'),
        ),
        migrations.AlterField(
            model_name='employmentinformation',
            name='line',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='generalsettings.line'),
        ),
        migrations.AlterField(
            model_name='employmentinformation',
            name='position',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='generalsettings.position'),
        ),
    ]
