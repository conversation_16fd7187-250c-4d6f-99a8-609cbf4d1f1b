# Generated by Django 5.2.3 on 2025-07-11 06:15

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('generalsettings', '0003_position'),
        ('userprofile', '0007_remove_contactperson_barangay_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='contactperson',
            name='address',
            field=models.TextField(null=True),
        ),
        migrations.AlterField(
            model_name='contactperson',
            name='contact_country_code',
            field=models.CharField(default='+63', max_length=5, null=True),
        ),
        migrations.AlterField(
            model_name='employmentinformation',
            name='approver',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervised_employees', to=settings.AUTH_USER_MODEL),
        ),
        migrations.Alter<PERSON>ield(
            model_name='employmentinformation',
            name='bank_account',
            field=models.Char<PERSON>ield(max_length=20, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='employmentinformation',
            name='department',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='generalsettings.department'),
        ),
        migrations.AlterField(
            model_name='employmentinformation',
            name='hdmf_number',
            field=models.CharField(max_length=15, null=True),
        ),
        migrations.AlterField(
            model_name='employmentinformation',
            name='line',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='generalsettings.line'),
        ),
        migrations.AlterField(
            model_name='employmentinformation',
            name='philhealth_number',
            field=models.CharField(max_length=15, null=True),
        ),
        migrations.AlterField(
            model_name='employmentinformation',
            name='position',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='generalsettings.position'),
        ),
        migrations.AlterField(
            model_name='employmentinformation',
            name='sss_number',
            field=models.CharField(max_length=15, null=True),
        ),
        migrations.AlterField(
            model_name='employmentinformation',
            name='tin_number',
            field=models.CharField(max_length=15, null=True),
        ),
        migrations.AlterField(
            model_name='personalinformation',
            name='birth_date',
            field=models.DateField(null=True),
        ),
        migrations.AlterField(
            model_name='personalinformation',
            name='birth_place',
            field=models.CharField(max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='personalinformation',
            name='contact_country_code',
            field=models.CharField(default='+63', max_length=5, null=True),
        ),
        migrations.AlterField(
            model_name='personalinformation',
            name='contact_number',
            field=models.CharField(max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='personalinformation',
            name='gender',
            field=models.CharField(choices=[('Male', 'Male'), ('Female', 'Female'), ('Other', 'Other')], max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='personalinformation',
            name='present_barangay',
            field=models.CharField(max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='personalinformation',
            name='present_city',
            field=models.CharField(max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='personalinformation',
            name='present_province',
            field=models.CharField(max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='personalinformation',
            name='provincial_barangay',
            field=models.CharField(max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='personalinformation',
            name='provincial_city',
            field=models.CharField(max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='personalinformation',
            name='provincial_province',
            field=models.CharField(max_length=100, null=True),
        ),
    ]
