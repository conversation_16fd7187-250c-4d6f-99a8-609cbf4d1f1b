# Generated by Django 5.2.3 on 2025-07-12 01:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('generalsettings', '0003_position'),
        ('userprofile', '0008_alter_contactperson_address_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='educationalbackground',
            options={'ordering': ['year_graduated', '-year_graduated', '-created_at']},
        ),
        migrations.AlterField(
            model_name='employmentinformation',
            name='approver',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervised_employees', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='employmentinformation',
            name='department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='generalsettings.department'),
        ),
        migrations.AlterField(
            model_name='employmentinformation',
            name='line',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='generalsettings.line'),
        ),
    ]
