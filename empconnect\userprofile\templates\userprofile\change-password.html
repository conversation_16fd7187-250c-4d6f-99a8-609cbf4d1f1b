{% extends "main.html" %}
{% load static %}

{% block title %}REPConnect - Profile{% endblock title %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'css/profile/profile.css' %}">
{% endblock %}

{% block content %}
<div class="page-content">
    <div class="page-header">
        <div class="page-header-content">
            <h1 class="page-title">Change Password</h1>
            <p class="page-subtitle">Update your account password to keep your account secure</p>
        </div>
    </div>

    <div class="component-card">
        <div class="password-cards-row">
            <div class="reminders-card">
                <div class="password-reminder">
                    <div class="password-reminder-header">
                        <div>
                            <h3 class="reminders-title">Password Security</h3>
                            <p class="reminders-subtitle">Keep your account secure by following these rules:</p>
                        </div>
                    </div>
                    <ul class="reminders-list">
                        <li>Use a strong password with at least 6+ characters</li>
                        <li>Include uppercase, lowercase, numbers & symbols</li>
                        <li>Do not reuse old passwords</li>
                        <li>Never share your password with anyone</li>
                        <li>Change your password every 90 days</li>
                        <li>Report suspicious activity to the IT Department</li>
                    </ul>
                </div>
            </div>
        <div class="password-card-body">
            <form id="passwordChangeForm" class="password-form" novalidate>
                    <div class="password-form-columns">
                        <div class="form-section left-col">
                            <h3 class="section-title">Change Password</h3>
                            <!-- Current Password field -->
                    <div class="form-group">
                                <label for="currentPassword" class="form-label">Current Password</label>
                                <div class="input-group modern-input-group">
                            <input 
                                type="password" 
                                id="currentPassword" 
                                name="currentPassword"
                                class="form-input" 
                                        placeholder="Current Password"
                                required
                                autocomplete="current-password"
                            >
                                    <button type="button" class="password-toggle" data-target="currentPassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="field-error" id="currentPasswordError"></div>
                    </div>
                            <!-- New Password field -->
                    <div class="form-group">
                                <label for="newPassword" class="form-label">New Password</label>
                                <div class="input-group modern-input-group">
                            <input 
                                type="password" 
                                id="newPassword" 
                                name="newPassword"
                                class="form-input" 
                                        placeholder="New Password"
                                required
                                autocomplete="new-password"
                                        aria-describedby="passwordTooltip"
                            >
                                    <button type="button" class="password-toggle" data-target="newPassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="field-error" id="newPasswordError"></div>
                                <div class="password-tooltip" id="passwordTooltip" style="display: none;">
                            <div class="tooltip-header">
                                        <h4 id="passwordTitle">Strong Password</h4>
                                        <div class="strength-lines">
                                            <span class="line"></span>
                                            <span class="line"></span>
                                            <span class="line"></span>
                                            <span class="line"></span>
                                        </div>
                            </div>
                            <div class="tooltip-content">
                                        <h5>Password Requirements:</h5>
                                        <ul class="requirements-list">
                                            <li data-requirement="uppercase">
                                                <i class="fas fa-check"></i>
                                                <span>At least 1 uppercase letter (A-Z)</span>
                                            </li>
                                            <li data-requirement="lowercase">
                                                <i class="fas fa-check"></i>
                                                <span>At least 1 lowercase letter (a-z)</span>
                                            </li>
                                            <li data-requirement="number">
                                                <i class="fas fa-check"></i>
                                                <span>At least 1 number (0-9)</span>
                                            </li>
                                            <li data-requirement="special">
                                                <i class="fas fa-check"></i>
                                                <span>At least 1 special character (!@#$%^&*)</span>
                                            </li>
                                        </ul>
                                </div>
                                </div>
                            </div>
                            <!-- Confirm Password field -->
                    <div class="form-group">
                                <label for="confirmPassword" class="form-label">Confirm New Password</label>
                                <div class="input-group modern-input-group">
                            <input 
                                type="password" 
                                id="confirmPassword" 
                                name="confirmPassword"
                                class="form-input" 
                                        placeholder="Confirm Password"
                                required
                                autocomplete="new-password"
                            >
                                    <button type="button" class="password-toggle" data-target="confirmPassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="field-error" id="confirmPasswordError"></div>
                    </div>
                </div>
                    </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <span class="btn-text">Update Password</span>
                        <div class="loading-spinner" style="display: none;">
                                <div class="spinner"></div>
                        </div>
                    </button>
                </div>
            </form>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div class="message-container" id="messageContainer"></div>
    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

</div>
</div>
<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-spinner">
        <div style="--size: 64px; --dot-size: 6px; --dot-count: 6; --color: var(--primary-color); --speed: 1s; --spread: 60deg;" class="dots">
          <div style="--i: 0;" class="dot"></div>
          <div style="--i: 1;" class="dot"></div>
          <div style="--i: 2;" class="dot"></div>
          <div style="--i: 3;" class="dot"></div>
          <div style="--i: 4;" class="dot"></div>
          <div style="--i: 5;" class="dot"></div>
        </div>
        <p>Updating your password...</p>
    </div>
</div>
{% endblock content %}

{% block extra_js %}
    <script src="https://cdn.jsdelivr.net/npm/driver.js@latest/dist/driver.min.js"></script>
    <script src="{% static 'js/profile/change-password.js' %}"></script>
{% endblock %}